import { useEffect, useState } from 'react';
import { formatCurrentDate } from '@/utils/formatUtils';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { X } from 'lucide-react';
import { Progress } from "@/components/ui/progress"

import { Voucher } from '@/lib/types';
import { BatchInformation } from '@/components/voucher-receiving/batch-information';
import { VoucherTabsContent } from '@/components/voucher-receiving/voucher-tabs-content';
import { RejectionCommentDialog } from '@/components/voucher-receiving/rejection-comment-dialog';
import { useVoucherProcessing } from '@/hooks/use-voucher-processing';
import { useAppStore } from '@/lib/store';

interface DepartmentVoucherReceivingProps {
  isOpen: boolean;
  voucherIds: string[];
  onClose: () => void;
}

export function DepartmentVoucherReceiving({
  isOpen,
  voucherIds,
  onClose
}: DepartmentVoucherReceivingProps) {
  // PRODUCTION FIX: Ensure voucherIds is always an array
  const safeVoucherIds = Array.isArray(voucherIds) ? voucherIds : [];

  const [currentVoucherId, setCurrentVoucherId] = useState<string | undefined>(
    safeVoucherIds.length > 0 ? safeVoucherIds[0] : undefined
  );
  const [tabIndex, setTabIndex] = useState(0);
  const [receivedVouchers, setReceivedVouchers] = useState<string[]>([]);

  // Use the voucher processing hook
  const {
    voucher,
    showRejectionDialog,
    setShowRejectionDialog,
    handleAccept,
    handleReject,
    handleConfirmRejection,
    isReturnedVoucher
  } = useVoucherProcessing(currentVoucherId);

  // Calculate how many vouchers we have left to process
  const totalVouchers = safeVoucherIds.length;
  const remainingVouchers = safeVoucherIds.filter(id => !receivedVouchers.includes(id)).length;
  const processedVouchers = totalVouchers - remainingVouchers;

  // Effect to move to the next voucher automatically when one is processed
  useEffect(() => {
    const unprocessedVouchers = safeVoucherIds.filter(id => !receivedVouchers.includes(id));
    if (unprocessedVouchers.length === 0) {
      // All vouchers processed
      return;
    }

    // If current voucher has been processed, move to the next one
    if (currentVoucherId && receivedVouchers.includes(currentVoucherId)) {
      setCurrentVoucherId(unprocessedVouchers[0]);
    } else if (!currentVoucherId && unprocessedVouchers.length > 0) {
      // If no current voucher, set to first unprocessed
      setCurrentVoucherId(unprocessedVouchers[0]);
    }
  }, [receivedVouchers, safeVoucherIds, currentVoucherId]);

  const handleVoucherAccept = () => {
    if (!currentVoucherId) return;
    handleAccept();
    setReceivedVouchers(prev => [...prev, currentVoucherId]);
  };

  const handleVoucherReject = () => {
    if (!currentVoucherId) return;
    handleReject();
  };

  const handleConfirmVoucherRejection = (voucherId: string, comment: string) => {
    handleConfirmRejection(voucherId, comment);
    setReceivedVouchers(prev => [...prev, voucherId]);
  };

  // When all vouchers are processed, close the modal after a delay
  useEffect(() => {
    if (processedVouchers === totalVouchers && totalVouchers > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [processedVouchers, totalVouchers, onClose]);

  // Handle manual tab changes
  const handleTabChange = (index: number) => {
    setTabIndex(index);

    // Find the voucher ID for this tab
    const unprocessedVouchers = safeVoucherIds.filter(id => !receivedVouchers.includes(id));
    if (index < unprocessedVouchers.length) {
      setCurrentVoucherId(unprocessedVouchers[index]);
    }
  };

  // For batch information - get real voucher details from store
  const vouchers = useAppStore((state) => state.vouchers);
  const receivingVouchers = safeVoucherIds.map(id => {
    const voucher = vouchers.find(v => v.id === id);
    return voucher || { id } as Voucher; // Fallback to minimal voucher if not found
  });

  const batchInfo = {
    batchesArray: [{
      dispatchTime: formatCurrentDate(),
      dispatchedBy: "AUDIT",
      vouchers: receivingVouchers
    }],
    processedVouchers: receivedVouchers,
    rejectedVouchers: [],
    receivingVouchers: receivingVouchers
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] bg-black text-white border-gray-800 p-0 flex flex-col overflow-hidden">
        <DialogHeader className="border-b border-gray-800 p-4 bg-gray-900/50 shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-white text-lg">
              RECEIVE VOUCHER FROM AUDIT
            </DialogTitle>
            <X className="h-4 w-4 cursor-pointer text-gray-500 hover:text-white" onClick={onClose} />
          </div>
        </DialogHeader>

        <div className="p-6 flex flex-col flex-1 min-h-0">
          <div className="flex gap-4 mb-6 shrink-0">
            <Progress
              value={(processedVouchers / totalVouchers) * 100}
              className="h-2"
            />
            <div className="text-xs text-gray-400">
              {processedVouchers}/{totalVouchers}
            </div>
          </div>

          <Tabs value={tabIndex.toString()} onValueChange={(v) => handleTabChange(parseInt(v))} className="flex flex-col flex-1 min-h-0">
            <TabsList className="grid grid-cols-2 shrink-0">
              <TabsTrigger value="0" className="data-[state=active]:bg-gray-800">
                VOUCHER DETAILS
              </TabsTrigger>
              <TabsTrigger value="1" className="data-[state=active]:bg-gray-800">
                BATCH INFORMATION
              </TabsTrigger>
            </TabsList>
            <div className="flex-1 min-h-0 overflow-hidden">
              <div className="overflow-y-auto h-full">
                <TabsContent value="0" className="h-full">
                  <div>
                    <VoucherTabsContent
                      voucher={voucher}
                      onAccept={handleVoucherAccept}
                      onReject={handleVoucherReject}
                      isReturned={isReturnedVoucher}
                    />
                  </div>
                </TabsContent>
                <TabsContent value="1" className="h-full">
                  <div>
                    <BatchInformation {...batchInfo} />
                  </div>
                </TabsContent>
              </div>
            </div>
          </Tabs>
        </div>
      </DialogContent>

      {showRejectionDialog && currentVoucherId && (
        <RejectionCommentDialog
          isOpen={showRejectionDialog}
          voucherId={currentVoucherId}
          onClose={() => setShowRejectionDialog(false)}
          onConfirm={handleConfirmVoucherRejection}
          isReturnedVoucher={isReturnedVoucher}
        />
      )}
    </Dialog>
  );
}

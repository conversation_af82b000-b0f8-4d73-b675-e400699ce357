import { useState, useEffect, useCallback } from 'react';
import { getSocket } from '@/lib/socket';
import { useAppStore } from '@/lib/store';
import { toast } from '@/hooks/use-toast';

interface UseDepartmentLockOptions {
  onLockAcquired?: () => void;
  onLockFailed?: () => void;
  onLockReleased?: () => void;
  onAccessBlocked?: (blockedBy: any) => void;
}

// REDESIGNED: Clean Department Lock Hook for Exclusive Access Control
export function useDepartmentLock(
  targetDepartment: string | null,
  options: UseDepartmentLockOptions = {}
) {
  const [isLocked, setIsLocked] = useState(false);
  const [isLockOwner, setIsLockOwner] = useState(false);
  const [lockOwner, setLockOwner] = useState<any>(null);
  const [canAccess, setCanAccess] = useState(true);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockedBy, setBlockedBy] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const currentUser = useAppStore((state) => state.currentUser);
  const socket = getSocket();

  const {
    onLockAcquired,
    onLockFailed,
    onLockReleased,
    onAccessBlocked
  } = options;

  // Check department access on mount and when department changes
  useEffect(() => {
    if (!targetDepartment || !currentUser || !socket) return;

    const checkAccess = () => {
      socket.emit('check_department_access', { targetDepartment }, (response: any) => {
        if (response.success) {
          setCanAccess(response.canAccess);
          setIsBlocked(response.isBlocked || false);
          setBlockedBy(response.blockedBy || null);

          if (response.isBlocked && onAccessBlocked) {
            onAccessBlocked(response.blockedBy);
          }
        }
      });
    };

    checkAccess();
  }, [targetDepartment, currentUser, socket, onAccessBlocked]);

  // Listen for department lock updates
  useEffect(() => {
    if (!socket || !targetDepartment) return;

    const handleLockUpdate = (data: any) => {
      if (data.department === targetDepartment) {
        setIsLocked(data.isLocked);
        setLockOwner(data.owner);
        setIsLockOwner(data.isLocked && data.owner?.id === currentUser?.id);
      }
    };

    const handleAccessBlocked = (data: any) => {
      if (data.department === targetDepartment) {
        setCanAccess(false);
        setIsBlocked(true);
        setBlockedBy(data.blockedBy);

        if (onAccessBlocked) {
          onAccessBlocked(data.blockedBy);
        }

        toast({
          title: "Access Blocked",
          description: data.message,
          variant: "destructive",
        });
      }
    };

    const handleAccessRestored = (data: any) => {
      if (data.department === targetDepartment) {
        setCanAccess(true);
        setIsBlocked(false);
        setBlockedBy(null);

        toast({
          title: "Access Restored",
          description: data.message,
        });
      }
    };

    socket.on('department_lock_update', handleLockUpdate);
    socket.on('department_access_blocked', handleAccessBlocked);
    socket.on('department_access_restored', handleAccessRestored);

    return () => {
      socket.off('department_lock_update', handleLockUpdate);
      socket.off('department_access_blocked', handleAccessBlocked);
      socket.off('department_access_restored', handleAccessRestored);
    };
  }, [socket, targetDepartment, currentUser?.id, onAccessBlocked]);

  // Acquire department lock
  const acquireLock = useCallback(async () => {
    if (!targetDepartment || !socket || !currentUser || isLoading) return;

    // Check if access is blocked first
    if (!canAccess || isBlocked) {
      toast({
        title: "Access Denied",
        description: `${targetDepartment} voucher hub is currently being accessed by ${blockedBy?.name}. Please wait until they finish.`,
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await new Promise<any>((resolve) => {
        socket.emit('department_lock_request', { targetDepartment }, resolve);
      });

      if (response.success) {
        setIsLocked(true);
        setIsLockOwner(true);
        setLockOwner(response.lock?.ownerName || currentUser.name);

        toast({
          title: "Access Granted",
          description: `You now have exclusive access to ${targetDepartment} voucher hub.`,
        });

        if (onLockAcquired) {
          onLockAcquired();
        }
      } else {
        toast({
          title: "Access Denied",
          description: response.message,
          variant: "destructive",
        });

        if (onLockFailed) {
          onLockFailed();
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to acquire access. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [targetDepartment, socket, currentUser, canAccess, isBlocked, blockedBy, isLoading, onLockAcquired, onLockFailed]);

  // Release department lock
  const releaseLock = useCallback(async () => {
    if (!targetDepartment || !socket || !currentUser || !isLockOwner || isLoading) return;

    setIsLoading(true);

    try {
      const response = await new Promise<any>((resolve) => {
        socket.emit('department_lock_release', { targetDepartment }, resolve);
      });

      if (response.success) {
        setIsLocked(false);
        setIsLockOwner(false);
        setLockOwner(null);

        toast({
          title: "Access Released",
          description: `You have released exclusive access to ${targetDepartment} voucher hub.`,
        });

        if (onLockReleased) {
          onLockReleased();
        }
      } else {
        toast({
          title: "Error",
          description: response.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to release access. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [targetDepartment, socket, currentUser, isLockOwner, isLoading, onLockReleased]);

  // AUTO-RELEASE: Navigation-based lock release (immediate, no loading state)
  const releaseOnNavigation = useCallback(async () => {
    if (!targetDepartment || !currentUser || !socket || !isLockOwner) return;

    try {
      socket.emit('navigation_lock_release', {
        targetDepartment
      }, (result: any) => {
        if (result.success) {
          console.log(`🔓 Navigation release successful for ${targetDepartment}`);
          // Update local state immediately
          setIsLocked(false);
          setIsLockOwner(false);
          setLockOwner(null);
          setCanAccess(true);
          setIsBlocked(false);
          setBlockedBy(null);
        }
      });
    } catch (error) {
      console.error('Failed to release lock on navigation:', error);
    }
  }, [targetDepartment, currentUser, socket, isLockOwner]);

  return {
    // Lock state
    isLocked,
    isLockOwner,
    lockOwner,

    // Access control
    canAccess,
    isBlocked,
    blockedBy,

    // Actions
    acquireLock,
    releaseLock,
    releaseOnNavigation,

    // Loading state
    isLoading
  };
}

// LEGACY: Keep the old useResourceLock for backward compatibility
export function useResourceLock(
  resourceType: string,
  resourceId: string | null,
  options: any = {},
  targetDepartment?: string
) {
  // For now, just return empty state to prevent breaking existing components
  // TODO: Migrate all components to use useDepartmentLock instead
  return {
    isLocked: false,
    isLockOwner: false,
    lockOwner: null,
    viewers: [],
    viewerCount: 0,
    isViewing: false,
    acquireLock: () => {},
    releaseLock: () => {},
    startViewing: () => {},
    stopViewing: () => {},
    sendActivity: () => {},
    lockKey: null
  };
}

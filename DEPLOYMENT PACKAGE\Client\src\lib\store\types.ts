import { User, Voucher, ProvisionalCashRecord, Notification, Department, VoucherBatch, BlacklistedVoucherId, TransactionStatus } from '../types';

// Store types for the Voucher Management System
export type VoucherStatus = 'pending' | 'approved' | 'rejected' | 'voided' | 'paid';
export type UserRole = 'ADMIN' | 'USER' | 'VIEWER';
export type NotificationType = 'info' | 'success' | 'warning' | 'error';
export type VoucherType = 'normal' | 'advance' | 'special' | 'imprest';
export type FiscalMonth = 'JAN' | 'FEB' | 'MAR' | 'APR' | 'MAY' | 'JUN' | 'JUL' | 'AUG' | 'SEP' | 'OCT' | 'NOV' | 'DEC';

// System Settings interface
export interface SystemSettings {
  fiscalYearStart: FiscalMonth;
  fiscalYearEnd: FiscalMonth;
  currentFiscalYear: number;
  systemTime: string; // Current system time override
  autoBackupEnabled: boolean;
  sessionTimeout: number; // minutes
  scheduledRolloverDate?: string; // Scheduled rollover date
  autoRolloverEnabled?: boolean; // Enable/disable automatic rollover
  useLiveTime?: boolean; // Use live server time vs override
}

export interface User {
  id: string;
  name: string;
  password: string; // In a real app, we would never store plaintext passwords
  role: UserRole;
  department: string;
  dateCreated: string;
  lastLogin?: string;
  isActive: boolean;
  _originalIsActive?: boolean; // Used for tracking changes in the UI
}

export interface Voucher {
  id: string;
  type: VoucherType;
  amount: number;
  payee: string;
  description: string;
  dateCreated: string;
  dateModified?: string;
  datePaid?: string;
  status: VoucherStatus;
  attachments: string[];
  createdBy: string;
  approvedBy?: string;
  paidBy?: string;
  batchId?: string;
  remarks?: string;
  financialYear: number;
}

export interface ProvisionalCashRecord {
  id: string;
  amount: number;
  purpose: string;
  recipient: string;
  department: string;
  dateIssued: string;
  dateExpected: string;
  dateReconciled?: string;
  status: 'pending' | 'reconciled' | 'overdue';
  remarks?: string;
  voucherId?: string;
  createdBy: string;
}

export interface VoucherBatch {
  id: string;
  name: string;
  dateCreated: string;
  createdBy: string;
  status: 'pending' | 'processing' | 'completed';
  voucherIds: string[];
  totalAmount: number;
  remarks?: string;
}

export interface Notification {
  id: string;
  message: string;
  type: NotificationType;
  dateCreated: string;
  isRead: boolean;
  targetUserId?: string;
  relatedItemId?: string;
  link?: string;
}

// Registration interface
export interface PendingRegistration {
  id: string;
  name: string;
  password: string;
  department: string;
  dateRequested: string;
  status: 'pending' | 'approved' | 'rejected';
}

// Password change request interface
export interface PasswordChangeRequest {
  id: string;
  user_id: string;
  user_name: string;
  user_department: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  requested_at: string;
  processed_at?: string;
  processed_by?: string;
  admin_notes?: string;
  requested_by_ip?: string;
}

export interface AuditAttachment {
  id: string;
  voucher_id: string;
  original_filename: string;
  stored_filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  uploaded_by: string;
  uploaded_at: string;
  is_active: boolean;
  uploader_name?: string;
}

// Resource lock and viewers
export interface ResourceLock {
  key: string;
  userId: string;
  userName: string;
  department: string;
  expiresAt: number;
  resourceType: string;
  resourceId: string;
  remainingTime: number;
}

export interface ResourceViewer {
  userId: string;
  userName: string;
  department: string;
}

export interface AppState {
  // Authentication
  currentUser: User | null;
  login: (department: Department, username: string, password: string, isGuest?: boolean) => boolean;
  logout: () => void;
  registerUser: (name: string, password: string, department: Department) => boolean;
  pendingRegistrations: PendingRegistration[];
  approvePendingUser: (registrationId: string) => boolean;
  rejectPendingUser: (registrationId: string) => boolean;
  setMockAdminUser: (user: User) => void;
  passwordChangeRequests: PasswordChangeRequest[];
  fetchPasswordChangeRequests: () => Promise<boolean>;
  approvePasswordChangeRequest: (requestId: string) => Promise<boolean>;
  rejectPasswordChangeRequest: (requestId: string, reason?: string) => Promise<boolean>;

  // Year Management
  selectedYear: number | null;
  availableYears: number[];
  setSelectedYear: (year: number) => void;
  fetchAvailableYears: () => Promise<void>;

  // Resource locks and viewers
  resourceLocks: ResourceLock[];
  updateResourceLock: (lockKey: string, isLocked: boolean, userId?: string) => void;
  updateResourceLocks: (locks: ResourceLock[]) => void;
  resourceViewers: Record<string, {
    viewers: ResourceViewer[],
    viewerCount: number
  }>;
  updateResourceViewers: (resourceKey: string, viewers: ResourceViewer[], viewerCount: number) => void;
  getResourceViewers: (resourceKey: string) => ResourceViewer[];
  getResourceViewerCount: (resourceKey: string) => number;

  // Users
  users: User[];
  addUser: (user: User) => void;
  updateUser: (userId: string, userData: Partial<User>) => void;
  changePassword: (department: Department, newPassword: string) => void;
  resetUserPassword: (userId: string, newPassword: string) => boolean;
  deleteUser: (userId: string) => void;
  fetchAllUsers: () => Promise<boolean>;

  // Vouchers
  vouchers: Voucher[];
  // Updated to match the actual implementation
  addVoucher: (voucher: Partial<Omit<Voucher, 'id'>>) => Voucher;
  addVoucherToStore: (voucher: Voucher) => void; // NEW: Add existing voucher to store
  updateVoucher: (voucherId: string, voucherData: Partial<Voucher>) => void;
  deleteVoucher: (voucherId: string) => void;

  // Voucher Batches
  voucherBatches: VoucherBatch[];
  createVoucherBatch: (department: Department, voucherIds: string[], dispatchedBy: string) => VoucherBatch;
  receiveVoucherBatch: (batchId: string, receivedVoucherIds: string[], rejectedVoucherIds: string[], rejectionComments?: Record<string, string>) => void;

  // Blacklisted Voucher IDs
  blacklistedVoucherIds: BlacklistedVoucherId[];
  addBlacklistedVoucherId: (voucherId: string) => void;

  // Provisional Cash Records
  provisionalCashRecords: ProvisionalCashRecord[];
  addProvisionalCashRecord: (record: Omit<ProvisionalCashRecord, 'id'>) => Promise<ProvisionalCashRecord>;
  updateProvisionalCashRecord: (recordId: string, recordData: Partial<ProvisionalCashRecord>) => Promise<ProvisionalCashRecord>;
  deleteProvisionalCashRecord: (recordId: string) => void;
  loadProvisionalCashRecords: () => Promise<void>;

  // Notifications
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => Notification;
  markNotificationAsRead: (notificationId: string) => void;
  deleteNotification: (notificationId: string) => void;
  fetchNotifications: () => Promise<void>;

  // Department-specific data
  getVouchersForDepartment: (department: Department) => Voucher[];
  getVoucherBatchesForDepartment: (department: Department) => VoucherBatch[];
  getPendingVouchersForDepartment: (department: Department) => Voucher[];
  getNotificationsForUser: (userId: string) => Notification[];

  // Audit operations
  sendVouchersToAudit: (department: Department, voucherIds: string[], dispatchedBy: string) => void;
  sendVouchersFromAuditToDepartment: (department: Department, voucherIds: string[]) => void;
  pendingDispatches: Set<string>; // Track pending dispatch operations to prevent duplicates

  // Admin operations
  exportToExcel: () => void;
  resetYearData: () => void;

  // Cleanup operations
  cleanupExpiredRejectedVouchers: () => void;
  cleanupExpiredReturnedVouchers: () => void;

  // New admin functions and system settings
  backupSystemData: () => boolean;
  restoreSystemData: (backupFile: File) => boolean;
  clearLocalStorage: () => boolean;
  configureSystem: (settings: Partial<SystemSettings>) => boolean;
  systemSettings: SystemSettings;
}

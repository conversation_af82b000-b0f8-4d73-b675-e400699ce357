import React, { useState, useEffect } from 'react';
import { Al<PERSON><PERSON><PERSON><PERSON>, Wifi, WifiOff, RefreshCw, CheckCircle } from 'lucide-react';

interface ServerInfo {
  host: string;
  port: number;
  url: string;
  version?: string;
  lastSeen?: number;
}

interface ServerDiscoveryProps {
  onServerFound: (server: ServerInfo) => void;
  onConnectionLost: () => void;
}

export const ServerDiscovery: React.FC<ServerDiscoveryProps> = ({
  onServerFound,
  onConnectionLost
}) => {
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [currentServer, setCurrentServer] = useState<ServerInfo | null>(null);
  const [discoveryStatus, setDiscoveryStatus] = useState<string>('Initializing...');
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'discovering'>('discovering');
  const [retryCount, setRetryCount] = useState(0);

  // Auto-discovery on component mount
  useEffect(() => {
    startDiscovery();
    
    // Start health monitoring
    const healthInterval = setInterval(checkServerHealth, 30000); // Check every 30 seconds
    
    return () => {
      clearInterval(healthInterval);
    };
  }, []);

  /**
   * Start server discovery process
   */
  const startDiscovery = async () => {
    if (isDiscovering) return;
    
    setIsDiscovering(true);
    setConnectionStatus('discovering');
    setDiscoveryStatus('Searching for VMS server...');
    
    try {
      // Method 1: Check if we're already on the server
      const currentHost = window.location.hostname;
      const currentPort = window.location.port;
      
      if (currentPort && currentHost !== 'localhost') {
        const serverInfo = {
          host: currentHost,
          port: parseInt(currentPort),
          url: `${window.location.protocol}//${currentHost}:${currentPort}`
        };
        
        if (await validateServer(serverInfo)) {
          handleServerFound(serverInfo);
          return;
        }
      }
      
      // Method 2: Try common ports on current host
      const commonPorts = [8080, 8081, 8082, 3000, 3001, 3002];
      
      for (const port of commonPorts) {
        setDiscoveryStatus(`Checking port ${port}...`);
        
        const serverInfo = {
          host: currentHost,
          port,
          url: `${window.location.protocol}//${currentHost}:${port}`
        };
        
        if (await validateServer(serverInfo)) {
          handleServerFound(serverInfo);
          return;
        }
      }
      
      // Method 3: Try localhost if not already tried
      if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
        for (const port of commonPorts) {
          setDiscoveryStatus(`Checking localhost:${port}...`);
          
          const serverInfo = {
            host: 'localhost',
            port,
            url: `http://localhost:${port}`
          };
          
          if (await validateServer(serverInfo)) {
            handleServerFound(serverInfo);
            return;
          }
        }
      }
      
      // Discovery failed
      setDiscoveryStatus('VMS server not found. Please check network connection.');
      setConnectionStatus('disconnected');
      
      // Retry after delay
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        startDiscovery();
      }, 5000);
      
    } catch (error) {
      console.error('Discovery error:', error);
      setDiscoveryStatus('Discovery failed. Retrying...');
      setConnectionStatus('disconnected');
      
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        startDiscovery();
      }, 5000);
    } finally {
      setIsDiscovering(false);
    }
  };

  /**
   * Validate if a server is the VMS server
   */
  const validateServer = async (serverInfo: ServerInfo): Promise<boolean> => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);
      
      const response = await fetch(`${serverInfo.url}/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        }
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        const health = await response.json();
        if (health.status === 'healthy' && health.version) {
          serverInfo.version = health.version;
          return true;
        }
      }
    } catch (error) {
      // Server not reachable or not VMS server
    }
    
    return false;
  };

  /**
   * Handle server found
   */
  const handleServerFound = (server: ServerInfo) => {
    console.log(`✅ VMS Server discovered: ${server.url}`);
    setCurrentServer({ ...server, lastSeen: Date.now() });
    setConnectionStatus('connected');
    setDiscoveryStatus(`Connected to VMS Server v${server.version || 'Unknown'}`);
    setRetryCount(0);
    
    onServerFound(server);
  };

  /**
   * Check server health
   */
  const checkServerHealth = async () => {
    if (!currentServer) return;
    
    const isHealthy = await validateServer(currentServer);
    
    if (!isHealthy) {
      console.warn('⚠️ Server connection lost, attempting rediscovery...');
      setCurrentServer(null);
      setConnectionStatus('disconnected');
      setDiscoveryStatus('Connection lost. Reconnecting...');
      
      onConnectionLost();
      
      // Attempt automatic rediscovery
      setTimeout(startDiscovery, 2000);
    } else {
      setCurrentServer(prev => prev ? { ...prev, lastSeen: Date.now() } : null);
    }
  };

  /**
   * Manual retry
   */
  const handleManualRetry = () => {
    setRetryCount(0);
    startDiscovery();
  };

  /**
   * Get status icon
   */
  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'discovering':
        return <RefreshCw className={`w-5 h-5 text-blue-500 ${isDiscovering ? 'animate-spin' : ''}`} />;
      case 'disconnected':
        return <WifiOff className="w-5 h-5 text-red-500" />;
      default:
        return <Wifi className="w-5 h-5 text-gray-500" />;
    }
  };

  /**
   * Get status color
   */
  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'discovering':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'disconnected':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  // Don't render if connected and working normally
  if (connectionStatus === 'connected' && !isDiscovering) {
    return null;
  }

  return (
    <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg border-2 shadow-lg max-w-sm ${getStatusColor()}`}>
      <div className="flex items-center space-x-3">
        {getStatusIcon()}
        <div className="flex-1">
          <div className="font-medium text-sm">
            {connectionStatus === 'connected' ? 'VMS Server Connected' : 
             connectionStatus === 'discovering' ? 'Finding VMS Server' : 
             'VMS Server Disconnected'}
          </div>
          <div className="text-xs opacity-75 mt-1">
            {discoveryStatus}
          </div>
          {currentServer && (
            <div className="text-xs opacity-75 mt-1">
              {currentServer.url}
            </div>
          )}
          {retryCount > 0 && (
            <div className="text-xs opacity-75 mt-1">
              Retry attempt: {retryCount}
            </div>
          )}
        </div>
        {connectionStatus === 'disconnected' && (
          <button
            onClick={handleManualRetry}
            className="p-1 rounded hover:bg-white hover:bg-opacity-20 transition-colors"
            title="Retry connection"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

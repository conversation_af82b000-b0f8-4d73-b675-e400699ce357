import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { BarChart3, CreditCard, Home, AlertTriangle } from 'lucide-react';
import { useAppStore } from '@/lib/store';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export function AuditNavigation() {
  const location = useLocation();
  const currentPath = location.pathname;
  const currentUser = useAppStore((state) => state.currentUser);
  // CRITICAL FIX: Use department-specific batch filtering
  const voucherBatches = useAppStore((state) =>
    currentUser ? state.getVoucherBatchesForDepartment(currentUser.department) : []
  );

  // Check if there are pending batches that need to be received
  const pendingBatches = Array.isArray(voucherBatches)
    ? voucherBatches.filter(batch => batch && !batch.received)
    : [];

  const hasPendingBatches = pendingBatches.length > 0;

  const isActive = (path: string) => currentPath === path;

  return (
    <div className="flex items-center space-x-2 mb-6">
      <Link to="/audit-dashboard">
        <Button
          variant={isActive('/audit-dashboard') ? "default" : "outline"}
          size="sm"
          className="flex items-center"
        >
          <Home className="mr-2 h-4 w-4" />
          Dashboard
        </Button>
      </Link>

      {hasPendingBatches ? (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center opacity-50 cursor-not-allowed"
                disabled
              >
                <CreditCard className="mr-2 h-4 w-4" />
                Provisional Cash
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>You must process new vouchers before accessing this section</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : (
        <Link to="/audit-dashboard/cash-record">
          <Button
            variant={isActive('/audit-dashboard/cash-record') ? "default" : "outline"}
            size="sm"
            className="flex items-center"
          >
            <CreditCard className="mr-2 h-4 w-4" />
            Provisional Cash
          </Button>
        </Link>
      )}

      {/* Analytics is always accessible */}
      <Link to="/audit-dashboard/analytics">
        <Button
          variant={isActive('/audit-dashboard/analytics') ? "default" : "outline"}
          size="sm"
          className="flex items-center"
        >
          <BarChart3 className="mr-2 h-4 w-4" />
          Analytics
        </Button>
      </Link>
    </div>
  );
}

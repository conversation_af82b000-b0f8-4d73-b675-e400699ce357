import { LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useAppStore } from "@/lib/store";
import { useNavigate } from "react-router-dom";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";

export function ExitButton() {
  const logout = useAppStore((state) => state.logout);
  const navigate = useNavigate();

  const handleLogout = () => {
    try {
      // Perform a complete logout
      logout();

      // Clear the persisted state from localStorage
      localStorage.removeItem('voucher-management-system');

      // Show success message
      toast.success('Logged out successfully');

      console.log('Complete logout performed');

      // Force a reload to ensure a fresh state
      window.location.href = '/?logout=true';
    } catch (error) {
      console.error('Error during logout:', error);
      toast.error('Error during logout');

      // Fallback: try to force a reload
      window.location.href = '/?logout=true';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="destructive"
            size="sm"
            onClick={handleLogout}
            className="h-9 px-3 bg-red-600 hover:bg-red-700 text-white border-red-500 shadow-lg hover:shadow-xl transition-all duration-200 font-medium"
          >
            <LogOut className="h-4 w-4 mr-2" />
            <span className="text-sm">Exit</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Logout from VMS</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

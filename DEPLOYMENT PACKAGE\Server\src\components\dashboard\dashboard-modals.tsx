
import { useAppStore } from '@/lib/store';
import { toast } from '@/hooks/use-toast';
import { Voucher } from '@/lib/types';
import { VoucherDetailsModal } from '@/components/voucher-details-modal';
import { DepartmentVoucherReceiving } from '@/components/department-voucher-receiving';
import { VoucherBatchReceiving } from '@/components/voucher-batch-receiving';

interface DashboardModalsProps {
  department: string;
  viewingVoucher: Voucher | null;
  setViewingVoucher: (voucher: Voucher | null) => void;
  showVoucherReceiving: boolean;
  setShowVoucherReceiving: (show: boolean) => void;
  receivingVoucherIds: string[];
  showBatchReceiving: boolean;
  setShowBatchReceiving: (show: boolean) => void;
  selectedBatchId: string;
  onRefresh?: () => void;
}

export function DashboardModals({
  department,
  viewingVoucher,
  setViewingVoucher,
  showVoucherReceiving,
  setShowVoucherReceiving,
  receivingVoucherIds,
  showBatchReceiving,
  setShowBatchReceiving,
  selectedBatchId,
  onRefresh
}: DashboardModalsProps) {
  const deleteVoucher = useAppStore((state) => state.deleteVoucher);

  const handleDeleteVoucher = (voucherId: string) => {
    try {
      console.log(`Deleting voucher with ID: ${voucherId}`);
      deleteVoucher(voucherId);
      setViewingVoucher(null);

      if (onRefresh) {
        onRefresh();
      }

      toast({
        title: "Voucher Deleted",
        description: "The voucher has been successfully deleted",
        variant: "default",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete voucher",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <>
      {showVoucherReceiving && (
        <DepartmentVoucherReceiving
          voucherIds={receivingVoucherIds}
          isOpen={showVoucherReceiving}
          onClose={() => setShowVoucherReceiving(false)}
        />
      )}

      {showBatchReceiving && selectedBatchId && (
        <VoucherBatchReceiving
          batchId={selectedBatchId}
          open={showBatchReceiving}
          onClose={() => setShowBatchReceiving(false)}
        />
      )}

      {viewingVoucher && (
        <VoucherDetailsModal
          voucher={viewingVoucher}
          onClose={() => {
            console.log('Closing voucher details modal');
            setViewingVoucher(null);
          }}
          showDelete={(viewingVoucher.status === "PENDING SUBMISSION" && !viewingVoucher.sentToAudit) ||
                      (viewingVoucher.status === "VOUCHER REJECTED")}
          onDelete={handleDeleteVoucher}
          showAddBack={
            viewingVoucher.status === "VOUCHER REJECTED" ||
            (viewingVoucher.status === "VOUCHER CERTIFIED" && viewingVoucher.isReturned)
          }
          department={department}
        />
      )}
    </>
  );
}

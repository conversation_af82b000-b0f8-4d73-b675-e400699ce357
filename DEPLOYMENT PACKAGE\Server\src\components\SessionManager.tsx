import React, { useEffect, useState } from 'react';
import { useAppStore } from '@/lib/store';
import { toast } from 'sonner';

// Simple session management for LAN deployment
const SessionManager: React.FC = () => {
  const { currentUser, logout } = useAppStore();
  const [isOnline, setIsOnline] = useState(true);
  const [sessionWarning, setSessionWarning] = useState(false);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // PRODUCTION OPTIMIZED: Balanced session configuration
  const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // 8 hours (aligned with backend)
  const INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes (optimal balance)
  const WARNING_THRESHOLD = 5 * 60 * 1000; // 5 minutes warning (reasonable for 30min timeout)
  const HEARTBEAT_INTERVAL = 5 * 60 * 1000; // 5 minutes heartbeat

  // Check network connectivity to server
  useEffect(() => {
    if (!currentUser) return;

    const checkConnection = async () => {
      try {
        const response = await fetch('/health', {
          method: 'HEAD',
          timeout: 3000  // Shorter timeout for LAN
        });
        
        if (response.ok) {
          if (!isOnline) {
            // Connection restored
            toast.success('✅ Connection restored');
            setShowNetworkError(false);
          }
          setIsOnline(true);
        } else {
          if (isOnline) {
            // Connection lost
            toast.error('⚠️ Cannot connect to server');
            setShowNetworkError(true);
          }
          setIsOnline(false);
        }
      } catch (error) {
        if (isOnline) {
          // Connection lost
          toast.error('⚠️ Cannot connect to server');
          setShowNetworkError(true);
        }
        setIsOnline(false);
      }
    };

    // Check connection every 2 minutes (LAN is more stable)
    const connectionInterval = setInterval(checkConnection, 120000);
    
    // Initial check
    checkConnection();

    return () => clearInterval(connectionInterval);
  }, [currentUser]);

  // PRODUCTION FIX: Enhanced session management with heartbeat
  useEffect(() => {
    if (!currentUser?.loginTime) return;

    let lastActivity = Date.now();

    // Send heartbeat to server to keep session alive
    const sendHeartbeat = async () => {
      try {
        const response = await fetch('/api/auth/heartbeat', {
          method: 'POST',
          credentials: 'include'
        });

        if (response.ok) {
          console.log('🫀 Session heartbeat sent successfully');
        } else {
          console.warn('⚠️ Session heartbeat failed:', response.status);
        }
      } catch (error) {
        console.error('❌ Session heartbeat error:', error);
      }
    };

    // Track user activity
    const updateActivity = () => {
      lastActivity = Date.now();
    };

    // Add activity listeners
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    const checkSessionTimeout = () => {
      const now = Date.now();
      const sessionAge = now - currentUser.loginTime;
      const inactivityAge = now - lastActivity;
      const timeLeft = SESSION_TIMEOUT - sessionAge;

      // Check inactivity timeout first (30 minutes)
      if (inactivityAge > INACTIVITY_TIMEOUT) {
        localStorage.setItem('auth_error', 'Your session expired due to inactivity (30 minutes). Please log in again.');
        logout();
        return;
      }

      // Check session timeout
      if (timeLeft <= 0) {
        localStorage.setItem('auth_error', 'Your session has expired. Please log in again.');
        logout();
        return;
      }

      // Show warning
      if (timeLeft <= WARNING_THRESHOLD && !sessionWarning) {
        const minutesLeft = Math.floor(timeLeft / (60 * 1000));
        setSessionWarning(true);

        toast.warning(
          `Session expires in ${minutesLeft} minutes. Click to extend or stay active.`,
          {
            duration: 15000, // Longer duration for 30-min timeout
            action: {
              label: 'Extend Session',
              onClick: async () => {
                await sendHeartbeat();
                useAppStore.setState({
                  currentUser: {
                    ...currentUser,
                    loginTime: Date.now()
                  }
                });
                setSessionWarning(false);
                toast.success('Session extended successfully');
              }
            }
          }
        );
      }
    };

    // Send heartbeat every 5 minutes
    const heartbeatInterval = setInterval(sendHeartbeat, HEARTBEAT_INTERVAL);

    // Check session every minute
    const sessionInterval = setInterval(checkSessionTimeout, 60000);

    return () => {
      clearInterval(heartbeatInterval);
      clearInterval(sessionInterval);

      // Remove activity listeners
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, [currentUser, sessionWarning, logout]);

  // PRODUCTION FIX: Extended work hours for 24/7 operation
  useEffect(() => {
    if (!currentUser) return;

    const checkWorkHours = () => {
      const now = new Date();
      const hour = now.getHours();

      // PRODUCTION FIX: Maintenance window compatible with 8-hour sessions
      // Only enforce maintenance if no active work is detected
      if (hour >= 2 && hour < 4) {
        // Check if user has been active in the last 30 minutes
        const recentActivity = Date.now() - lastActivity < (30 * 60 * 1000);

        if (!recentActivity) {
          toast.info('System maintenance window: Please save your work and log back in after 4 AM');
          // Give users 5 minutes warning before logout
          setTimeout(() => {
            logout();
          }, 5 * 60 * 1000); // 5 minutes delay
        } else {
          // User is actively working - skip maintenance logout
          console.log('⚠️ Maintenance window skipped - user is actively working');
        }
        return;
      }

      // For production VMS, allow access during all business hours
      // No automatic logout based on time - only session timeout and inactivity
    };

    // Check maintenance window every hour
    const workHoursInterval = setInterval(checkWorkHours, 60 * 60 * 1000);

    return () => clearInterval(workHoursInterval);
  }, [currentUser, logout]);

  // Auto-dismiss network error notification
  useEffect(() => {
    if (showNetworkError) {
      const timer = setTimeout(() => {
        setShowNetworkError(false);
      }, 5000); // Auto-dismiss after 5 seconds

      return () => clearTimeout(timer);
    }
  }, [showNetworkError]);

  // Show temporary network error notification (auto-dismissing)
  if (currentUser && showNetworkError) {
    return (
      <div className="fixed bottom-4 left-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-40 max-w-sm animate-in slide-in-from-left duration-300">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">
            ⚠️ Network connection lost
          </span>
          <button
            onClick={() => setShowNetworkError(false)}
            className="ml-2 text-white hover:text-gray-200 text-lg leading-none"
            aria-label="Dismiss"
          >
            ×
          </button>
        </div>
      </div>
    );
  }

  return null;
};

export default SessionManager;

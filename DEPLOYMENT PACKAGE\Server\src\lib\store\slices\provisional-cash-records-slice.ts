
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { provisionalCashApi } from '@/lib/api';

export interface ProvisionalCashRecordsSlice {
  provisionalCashRecords: AppState['provisionalCashRecords'];
  addProvisionalCashRecord: AppState['addProvisionalCashRecord'];
  updateProvisionalCashRecord: AppState['updateProvisionalCashRecord'];
  deleteProvisionalCashRecord: AppState['deleteProvisionalCashRecord'];
  loadProvisionalCashRecords: () => Promise<void>;
}

export const createProvisionalCashRecordsSlice: StateCreator<AppState, [], [], ProvisionalCashRecordsSlice> = (set) => ({
  // PRODUCTION: Start with empty array, load from API
  provisionalCashRecords: [],
  addProvisionalCashRecord: async (record) => {
    try {
      // Create record via API
      const newRecord = await provisionalCashApi.createRecord({
        ...record,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      // Add to local state
      set((state) => ({
        provisionalCashRecords: [...state.provisionalCashRecords, newRecord],
      }));

      return newRecord;
    } catch (error) {
      console.error('Failed to create provisional cash record:', error);
      throw error;
    }
  },
  updateProvisionalCashRecord: async (recordId, recordData) => {
    try {
      // Update record via API
      const updatedRecord = await provisionalCashApi.updateRecord(recordId, recordData);

      // Update local state
      set((state) => ({
        provisionalCashRecords: state.provisionalCashRecords.map(record => {
          if (record.id === recordId) {
            // If amountRetired is undefined or null, also clear related fields
            if (recordData.amountRetired === undefined || recordData.amountRetired === null) {
              return {
                ...record,
                ...recordData,
                amountRetired: undefined,
                clearanceRemark: undefined,
                dateRetired: undefined,
                clearedBy: undefined
              };
            }
            return { ...record, ...recordData };
          }
          return record;
        })
      }));

      return updatedRecord;
    } catch (error) {
      console.error('Failed to update provisional cash record:', error);
      throw error;
    }
  },
  deleteProvisionalCashRecord: (recordId) => set((state) => ({
    provisionalCashRecords: state.provisionalCashRecords.filter(record => record.id !== recordId)
  })),
  loadProvisionalCashRecords: async () => {
    try {
      console.log('🔄 Loading provisional cash records from API...');
      const records = await provisionalCashApi.getAllRecords();
      console.log(`✅ Loaded ${records.length} provisional cash records`);

      set({ provisionalCashRecords: records });
    } catch (error) {
      console.error('❌ Failed to load provisional cash records:', error);
    }
  },
});

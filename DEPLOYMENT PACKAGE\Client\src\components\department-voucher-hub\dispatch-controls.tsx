import React, { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Department } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { formatCurrentDate } from '@/lib/store/utils';

interface DispatchControlsProps {
  pendingDispatchVouchersCount: number;
  dispatchedBy: string;
  customDispatchName: string;
  selectedVouchers: string[];
  setDispatchedBy: (value: string) => void;
  setCustomDispatchName: (value: string) => void;
  handleSendToDepartment: () => void;
  department: Department;
}

export function DispatchControls({
  pendingDispatchVouchersCount,
  dispatchedBy,
  customDispatchName,
  selectedVouchers,
  setDispatchedBy,
  setCustomDispatchName,
  handleSendToDepartment,
  department,
}: DispatchControlsProps) {
  const selectedCount = selectedVouchers.length;
  const users = useAppStore((state) => state.users);

  // INFINITE LOOP FIX: Memoize the department users to prevent re-creation on every render
  const departmentUsers = useMemo(() => {
    // When in Audit side, we should show Audit users regardless of the department we're viewing
    // CRITICAL FIX: Include isActive filter to match other dropdown implementations
    return users.filter(user => user.department === "AUDIT" && user.isActive).map(user => user.name);
  }, [users]);
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const vouchers = useAppStore((state) => state.vouchers);

  const handleDispatchByChange = (value: string) => {
    setDispatchedBy(value);
    // PRODUCTION FIX: Do NOT update vouchers when dispatcher is selected
    // Vouchers should only be updated when dispatch is actually confirmed
  };

  const handleCustomNameChange = (value: string) => {
    const upperValue = value.toUpperCase();
    setCustomDispatchName(upperValue);

    // Only update if there's a value and selected vouchers
    if (upperValue.trim() && selectedVouchers.length > 0) {
      const currentTime = formatCurrentDate();

      // Update each selected voucher
      selectedVouchers.forEach(voucherId => {
        const voucher = vouchers.find(v => v.id === voucherId);
        if (voucher) {
          // Update voucher with custom dispatch info

          updateVoucher(voucherId, {
            dispatchedBy: upperValue,
            dispatchTime: currentTime
          });
        }
      });
    }
  };

  return (
    <div className="mb-4 flex flex-col sm:flex-row gap-4 items-center justify-between border-b pb-4">
      <div className="flex flex-col w-full sm:w-auto">
        <Label htmlFor="dispatch-person" className="mb-2">SELECT AUDIT OFFICER DISPATCHING THESE VOUCHERS</Label>
        <div className="flex gap-2">
          <Select value={dispatchedBy} onValueChange={handleDispatchByChange}>
            <SelectTrigger className="w-full sm:w-56 uppercase">
              <SelectValue placeholder="SELECT PERSON" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="NO_SELECTION" className="uppercase">SELECT PERSON</SelectItem>
              {departmentUsers.map(user => (
                <SelectItem key={user} value={user} className="uppercase">{user}</SelectItem>
              ))}
              <SelectItem value="OTHER" className="uppercase">OTHER</SelectItem>
            </SelectContent>
          </Select>

          {dispatchedBy === "OTHER" && (
            <Input
              placeholder="ENTER NAME"
              className="uppercase"
              value={customDispatchName}
              onChange={(e) => handleCustomNameChange(e.target.value)}
            />
          )}
        </div>
      </div>

      <Button
        size="lg"
        onClick={handleSendToDepartment}
        disabled={selectedCount === 0 || (dispatchedBy === "NO_SELECTION" && !customDispatchName)}
        variant="success"
        className="w-full sm:w-auto mt-4 sm:mt-0 uppercase"
      >
        SEND TO {department} ({selectedCount})
      </Button>
    </div>
  );
}


import { useAppStore } from '@/lib/store';
import { formatCurrentDate } from '@/lib/store/utils';
import { toast } from 'sonner';

export const useVoucherDeletion = () => {
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const deleteVoucher = useAppStore((state) => state.deleteVoucher);
  
  const handleDeleteReturnedVoucher = (voucherId: string) => {
    try {
      updateVoucher(voucherId, { 
        deleted: true,
        deletionTime: formatCurrentDate()
      });
      
      toast.success('RETURNED VOUCHER MARKED FOR DELETION', {
        duration: 3000,
      });
    } catch (error) {
      toast.error('FAILED TO DELETE RETURNED VOUCHER', {
        duration: 3000,
      });
    }
  };
  
  const handleDeleteRejectedVoucher = (voucherId: string) => {
    try {
      deleteVoucher(voucherId);
      
      toast.success('REJECTED VOUCHER DELETED', {
        duration: 3000,
      });
    } catch (error) {
      toast.error('FAILED TO DELETE REJECTED VOUCHER', {
        duration: 3000,
      });
    }
  };
  
  return {
    handleDeleteReturnedVoucher,
    handleDeleteRejectedVoucher
  };
};

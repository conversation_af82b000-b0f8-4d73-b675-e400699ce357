/**
 * New Voucher Tabs Hook - Based on Workflow State Machine
 * Single Source of Truth for tab filtering and voucher organization
 */

import { useMemo, useState } from 'react';
import { VoucherWorkflowStateMachine, type Voucher, type TabCounts } from '../../../lib/workflow/VoucherWorkflowStateMachine';

export interface VoucherTabsResult {
  // Tab state management
  activeTab: string;
  setActiveTab: (tab: string) => void;

  // Tab-specific voucher lists
  newVouchers: Voucher[];
  pendingDispatchVouchers: Voucher[];
  dispatchedVouchers: Voucher[];
  rejectedVouchers: Voucher[];
  returnedVouchers: Voucher[];

  // Finance-specific tabs
  pendingVouchers: Voucher[];
  processingVouchers: Voucher[];
  certifiedVouchers: Voucher[];

  // Tab counts
  tabCounts: TabCounts;

  // Helper functions
  getVouchersForTab: (tabName: string) => Voucher[];
  getTabDisplayName: (tabName: string) => string;
  getAvailableTabs: () => string[];
  isVoucherEditable: (voucher: Voucher) => boolean;
  canDispatchVoucher: (voucher: Voucher) => boolean;
  canResubmitVoucher: (voucher: Voucher) => boolean;
  getAvailableActions: (voucher: Voucher) => string[];
}

export function useVoucherTabs(
  vouchers: Voucher[],
  department: string
): VoucherTabsResult {
  
  const result = useMemo(() => {
    // Sort vouchers by priority
    const sortedVouchers = VoucherWorkflowStateMachine.sortVouchers(vouchers);
    
    // Get tab counts
    const tabCounts = VoucherWorkflowStateMachine.getTabCounts(sortedVouchers, department);
    
    // Helper function to get vouchers for specific tab
    const getVouchersForTab = (tabName: string): Voucher[] => {
      return VoucherWorkflowStateMachine.getVouchersForTab(sortedVouchers, tabName, department);
    };


    
    // Get vouchers for each tab
    const tabVouchers = {
      // Audit tabs
      newVouchers: getVouchersForTab('new-vouchers'),
      pendingDispatchVouchers: getVouchersForTab('pending-dispatch'),
      dispatchedVouchers: getVouchersForTab('dispatched'),
      rejectedVouchers: getVouchersForTab('rejected'),
      returnedVouchers: getVouchersForTab('returned'),

      // Finance tabs
      pendingVouchers: getVouchersForTab('pending'),
      processingVouchers: getVouchersForTab('processing'),
      certifiedVouchers: getVouchersForTab('certified')
    };
    
    // Helper functions
    const getTabDisplayName = (tabName: string): string => {
      return VoucherWorkflowStateMachine.getTabDisplayName(tabName);
    };
    
    const getAvailableTabs = (): string[] => {
      return VoucherWorkflowStateMachine.getAvailableTabs(department);
    };
    
    const isVoucherEditable = (voucher: Voucher): boolean => {
      return VoucherWorkflowStateMachine.isVoucherEditable(voucher, department);
    };
    
    const canDispatchVoucher = (voucher: Voucher): boolean => {
      return VoucherWorkflowStateMachine.canDispatchVoucher(voucher, department);
    };
    
    const canResubmitVoucher = (voucher: Voucher): boolean => {
      return VoucherWorkflowStateMachine.canResubmitVoucher(voucher, department);
    };
    
    const getAvailableActions = (voucher: Voucher): string[] => {
      return VoucherWorkflowStateMachine.getAvailableActions(voucher, department);
    };
    
    return {
      ...tabVouchers,
      tabCounts,
      getVouchersForTab,
      getTabDisplayName,
      getAvailableTabs,
      isVoucherEditable,
      canDispatchVoucher,
      canResubmitVoucher,
      getAvailableActions
    };
    
  }, [vouchers, department]);
  
  return result;
}

// Additional hook for real-time tab updates
export function useVoucherTabUpdates(
  vouchers: Voucher[],
  department: string,
  onTabChange?: (tabName: string, count: number) => void
) {
  const { tabCounts } = useVoucherTabs(vouchers, department);
  
  // Notify parent component of tab count changes
  useMemo(() => {
    if (onTabChange) {
      Object.entries(tabCounts).forEach(([tabName, count]) => {
        onTabChange(tabName, count);
      });
    }
  }, [tabCounts, onTabChange]);
  
  return tabCounts;
}

// Hook for voucher badge management
export function useVoucherBadges() {
  return {
    shouldShowBadge: VoucherWorkflowStateMachine.shouldShowBadge,
    getBadgeDisplayName: VoucherWorkflowStateMachine.getBadgeDisplayName,
    getBadgeClasses: VoucherWorkflowStateMachine.getBadgeClasses
  };
}

// Hook for voucher filtering and searching
export function useVoucherFiltering(
  vouchers: Voucher[],
  department: string,
  searchTerm?: string,
  dateRange?: { start: Date; end: Date },
  statusFilter?: string
) {
  return useMemo(() => {
    let filtered = vouchers;
    
    // Apply search filter
    if (searchTerm && searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(voucher => 
        voucher.voucher_id.toLowerCase().includes(term) ||
        voucher.description?.toLowerCase().includes(term) ||
        voucher.payee_name?.toLowerCase().includes(term)
      );
    }
    
    // Apply date range filter
    if (dateRange) {
      filtered = filtered.filter(voucher => {
        const voucherDate = new Date(voucher.created_at);
        return voucherDate >= dateRange.start && voucherDate <= dateRange.end;
      });
    }
    
    // Apply status filter
    if (statusFilter && statusFilter !== 'all') {
      filtered = filtered.filter(voucher => {
        const tab = VoucherWorkflowStateMachine.getTabForVoucher(voucher, department);
        return tab === statusFilter;
      });
    }
    
    return VoucherWorkflowStateMachine.sortVouchers(filtered);
  }, [vouchers, department, searchTerm, dateRange, statusFilter]);
}

// Export types for use in components
export type { Voucher, TabCounts } from '../../../lib/workflow/VoucherWorkflowStateMachine';
export { WorkflowState, BadgeType } from '../../../lib/workflow/VoucherWorkflowStateMachine';

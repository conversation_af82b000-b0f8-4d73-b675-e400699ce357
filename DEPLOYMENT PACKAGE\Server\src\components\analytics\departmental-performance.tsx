import { useState } from 'react';
import { Bar, <PERSON><PERSON><PERSON>, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YA<PERSON>s } from 'recharts';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatNumberWithCommas } from '@/utils/formatUtils';

interface DepartmentDataPoint {
  name: string;
  value: number;
}

interface DepartmentalData {
  voucherCount: DepartmentDataPoint[];
  rejectionRate: DepartmentDataPoint[];
  avgValue: DepartmentDataPoint[];
  processingTime: DepartmentDataPoint[];
}

interface DepartmentalPerformanceProps {
  data: DepartmentalData;
}

export function DepartmentalPerformance({ data }: DepartmentalPerformanceProps) {
  const [metric, setMetric] = useState<'voucherCount' | 'rejectionRate' | 'avgValue' | 'processingTime'>('voucherCount');
  
  const metricData = {
    voucherCount: {
      data: data.voucherCount,
      label: 'Voucher Count',
      valueFormatter: (value: number) => value.toString(),
      fill: '#6366f1'
    },
    rejectionRate: {
      data: data.rejectionRate,
      label: 'Rejection Rate (%)',
      valueFormatter: (value: number) => `${value}%`,
      fill: '#ef4444'
    },
    avgValue: {
      data: data.avgValue,
      label: 'Average Value',
      valueFormatter: (value: number) => `GHS ${formatNumberWithCommas(value)}`,
      fill: '#10b981'
    },
    processingTime: {
      data: data.processingTime,
      label: 'Avg. Processing Time (hrs)',
      valueFormatter: (value: number) => `${value} hrs`,
      fill: '#f59e0b'
    }
  };
  
  const currentMetric = metricData[metric];
  
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Card className="bg-background border-border p-2">
          <CardContent className="p-2">
            <p className="font-bold">{label}</p>
            <p className="text-sm">
              {currentMetric.label}: {currentMetric.valueFormatter(payload[0].value)}
            </p>
          </CardContent>
        </Card>
      );
    }
    return null;
  };

  return (
    <div className="space-y-4">
      <Tabs value={metric} onValueChange={(v) => setMetric(v as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="voucherCount">Count</TabsTrigger>
          <TabsTrigger value="rejectionRate">Rejection</TabsTrigger>
          <TabsTrigger value="avgValue">Value</TabsTrigger>
          <TabsTrigger value="processingTime">Time</TabsTrigger>
        </TabsList>
      </Tabs>
      
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={currentMetric.data}
            layout="vertical"
            margin={{ top: 5, right: 30, left: 60, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis 
              dataKey="name" 
              type="category" 
              width={80}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar 
              dataKey="value" 
              name={currentMetric.label} 
              fill={currentMetric.fill} 
              radius={[0, 4, 4, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}

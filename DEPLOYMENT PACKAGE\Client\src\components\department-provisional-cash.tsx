import { useState, useRef, useEffect, useMemo } from 'react';
import { formatNumberWithCommas, formatCurrentDate, formatVMSDateTime } from '@/utils/formatUtils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Search, ArrowLeft, Edit, Lock, Unlock } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAppStore } from '@/lib/store';
import { Department, ProvisionalCashRecord, ClearanceRemark } from '@/lib/types';
import { calculateClearanceRemark } from '@/lib/data';
import { toast } from '@/hooks/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useResourceLock } from '@/hooks/use-resource-lock';
import { Badge } from '@/components/ui/badge';
import { ViewerBadge } from '@/components/viewer-badge';

interface DepartmentProvisionalCashProps {
  department: Department;
  onBack: () => void;
  isEditable?: boolean;
}

export function DepartmentProvisionalCash({ department, onBack, isEditable: propIsEditable = true }: DepartmentProvisionalCashProps) {
  const provisionalCashRecords = useAppStore((state) => state.provisionalCashRecords);
  const updateProvisionalCashRecord = useAppStore((state) => state.updateProvisionalCashRecord);
  const loadProvisionalCashRecords = useAppStore((state) => state.loadProvisionalCashRecords);
  const currentUser = useAppStore((state) => state.currentUser);
  const users = useAppStore((state) => state.users);

  // Determine if this is an Audit user viewing another department's records
  const isAudit = currentUser?.department === 'AUDIT';
  const isViewingOtherDept = isAudit && department !== 'AUDIT';

  // Track if the user has clicked the release button
  const [isReleasing, setIsReleasing] = useState(false);

  // Use resource lock with department-specific targeting if in Audit
  const {
    isLocked,
    isLockOwner,
    lockOwnerName,
    acquireLock,
    releaseLock,
    isEditable: lockIsEditable,
    viewers,
    viewerCount,
    isViewing
  } = useResourceLock(
    'provisional-cash',
    department,
    {
      autoRelease: true,
      onLockAcquired: () => {
        console.log(`Acquired lock for ${department} provisional cash`);
        toast({
          title: "Editor Rights Acquired",
          description: `You are now editing ${department} provisional cash records.`,
          variant: "default",
        });
      },
      onLockReleased: () => {
        console.log(`Released lock for ${department} provisional cash`);
        toast({
          title: "Editor Rights Released",
          description: `You are no longer editing ${department} provisional cash records.`,
          variant: "default",
        });
      }
    },
    // Pass the target department if this is an Audit user viewing another department
    isViewingOtherDept ? department : undefined
  );

  // Function to handle releasing editor rights
  const handleReleaseEditorRights = async () => {
    setIsReleasing(true);
    await releaseLock();
    setIsReleasing(false);
  };

  // Combine the prop isEditable with the lock isEditable
  const isEditable = propIsEditable && (isViewingOtherDept ? lockIsEditable : true);

  // For edit button specifically, be more permissive - allow editing if user has basic permissions
  const isEditButtonEnabled = propIsEditable && currentUser && (currentUser.department === 'AUDIT' || currentUser.department === 'SYSTEM ADMIN');

  // Load provisional cash records when component mounts
  useEffect(() => {
    console.log(`🔄 PROVISIONAL CASH: Loading records for ${department} department...`);
    loadProvisionalCashRecords();
  }, [department, loadProvisionalCashRecords]);

  // Automatically acquire editor rights when component mounts
  useEffect(() => {
    if (isViewingOtherDept && !isLocked && !isLockOwner) {
      acquireLock();
    }
  }, [isViewingOtherDept, isLocked, isLockOwner, acquireLock]);

  const [searchTerm, setSearchTerm] = useState('');
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<ProvisionalCashRecord | null>(null);
  const [amountRetired, setAmountRetired] = useState('');
  const [comment, setComment] = useState('');
  const [clearedBy, setClearedBy] = useState('');

  // Inline editing state
  const [inlineEditingRecord, setInlineEditingRecord] = useState<string | null>(null);
  const [inlineAmountRetired, setInlineAmountRetired] = useState('');
  const [inlineComment, setInlineComment] = useState('');

  // Load provisional cash records when component mounts
  useEffect(() => {
    loadProvisionalCashRecords();
  }, [loadProvisionalCashRecords]);

  // Filter records by department - use originalDepartment to show all records that originated from this department
  const departmentRecords = provisionalCashRecords.filter(record => {
    const voucherId = record.voucherId;
    const vouchers = useAppStore.getState().vouchers;
    const voucher = vouchers.find(v => v.id === voucherId);
    // Use originalDepartment to show records from vouchers that originated from this department
    // This ensures Finance department can see provisional cash records even when vouchers are still in audit
    return voucher?.originalDepartment === department || voucher?.department === department;
  });

  // Apply search filter
  const filteredRecords = departmentRecords.filter(record =>
    record.voucherRef.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.claimant.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // INFINITE LOOP FIX: Memoize Audit users for the clearedBy dropdown - only Audit users should be able to clear provisional cash records
  const departmentUsers = useMemo(() => {
    return users.filter(user => user.department === "AUDIT");
  }, [users]);

  const getClearanceClass = (remark?: string) => {
    if (!remark) return "";

    switch (remark) {
      case "CLEARED":
        return "text-emerald-600 dark:text-emerald-400";
      case "REFUNDED TO CHEST":
        return "text-blue-600 dark:text-blue-400";
      case "DUE STAFF":
        return "text-amber-600 dark:text-amber-400";
      default:
        return "";
    }
  };

  const handleEditRecord = (record: ProvisionalCashRecord) => {
    setCurrentRecord(record);
    setAmountRetired(record.amountRetired?.toString() || '');
    setComment(record.comment || '');
    setClearedBy(record.clearedBy || currentUser?.name || '');
    setEditDialogOpen(true);
  };

  const handleSaveEdit = async () => {
    if (!currentRecord) return;

    const amountRetiredValue = parseFloat(amountRetired);

    if (isNaN(amountRetiredValue) || amountRetiredValue < 0) {
      toast({
        title: 'Invalid amount',
        description: 'Amount retired must be a non-negative number (0.00 or greater)',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Calculate clearance remark
      const { remark } = calculateClearanceRemark(currentRecord.mainAmount, amountRetiredValue);

      // Update the record
      await updateProvisionalCashRecord(currentRecord.id, {
        amountRetired: amountRetiredValue,
        clearanceRemark: remark,
        // Use formatCurrentDate to ensure consistent date format with NEW VOUCHERS tab
        dateRetired: formatCurrentDate(),
        clearedBy,
        comment
      });

      toast({
        title: 'Record Updated',
        description: `Cash record for voucher ${currentRecord.voucherRef} has been updated`,
      });

      // Close dialog and reset state
      setEditDialogOpen(false);
      setCurrentRecord(null);
      setAmountRetired('');
      setComment('');
      setClearedBy('');
    } catch (error) {
      console.error('Failed to update provisional cash record:', error);
      toast({
        title: 'Update Failed',
        description: 'Failed to update the provisional cash record. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Inline editing functions
  const handleStartInlineEdit = (record: ProvisionalCashRecord) => {
    setInlineEditingRecord(record.id);
    setInlineAmountRetired(record.amountRetired?.toString() || '');
    setInlineComment(record.comment || '');
  };

  const handleCancelInlineEdit = () => {
    setInlineEditingRecord(null);
    setInlineAmountRetired('');
    setInlineComment('');
  };

  const handleSaveInlineEdit = async (record: ProvisionalCashRecord) => {
    if (!inlineEditingRecord) return;

    const amountRetiredValue = parseFloat(inlineAmountRetired);

    if (isNaN(amountRetiredValue) || amountRetiredValue < 0) {
      toast({
        title: 'Invalid amount',
        description: 'Amount retired must be a non-negative number (0.00 or greater)',
        variant: 'destructive',
      });
      return;
    }

    try {
      const { remark } = calculateClearanceRemark(record.mainAmount, amountRetiredValue);

      await updateProvisionalCashRecord(record.id, {
        amountRetired: amountRetiredValue,
        clearanceRemark: remark,
        dateRetired: formatCurrentDate(),
        clearedBy: currentUser?.name,
        comment: inlineComment
      });

      toast({
        title: 'Record Updated',
        description: `Cash record for voucher ${record.voucherRef} has been updated`,
      });

      handleCancelInlineEdit();
    } catch (error) {
      console.error('Failed to update provisional cash record:', error);
      toast({
        title: 'Update Failed',
        description: 'Failed to update the provisional cash record. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle ESC key to cancel inline editing
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && inlineEditingRecord) {
        handleCancelInlineEdit();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [inlineEditingRecord]);

  return (
    <div className="space-y-6 flex flex-col h-full">
      <div className="flex items-center shrink-0">
        <Button variant="outline" size="icon" onClick={onBack} className="mr-2 border-primary hover:bg-primary/10">
          <ArrowLeft className="h-5 w-5 text-primary" />
        </Button>
        <h2 className="text-xl font-semibold">{department} Provisional Cash Records</h2>

        {/* Always show viewer badge */}
        <div className="flex items-center gap-2 ml-auto">
          {/* Viewer badge - always shown */}
          <ViewerBadge
            viewers={viewers}
            viewerCount={viewerCount}
            isViewing={isViewing}
          />

          {/* Only show lock controls for Audit users viewing other departments */}
          {isViewingOtherDept && (
            <>
              {/* Lock status - only shown when someone has acquired edit rights */}
              {isLockOwner ? (
                <>
                  <Badge variant="default" className="bg-green-600">
                    <Lock className="h-3 w-3 mr-1" />
                    EDITING
                  </Badge>
                  <Button
                    variant={isReleasing ? "default" : "outline"}
                    onClick={handleReleaseEditorRights}
                    className={`uppercase ${isReleasing ? "bg-red-500 text-white" : "text-red-500 border-red-500 hover:bg-red-500/10"}`}
                    size="sm"
                  >
                    <Unlock className="h-4 w-4 mr-1" />
                    RELEASE
                  </Button>
                </>
              ) : isLocked ? (
                <Badge variant="outline" className="border-yellow-600 text-yellow-600">
                  <Lock className="h-3 w-3 mr-1" />
                  LOCKED BY {lockOwnerName}
                </Badge>
              ) : null}
            </>
          )}
        </div>
      </div>

      <div className="flex justify-between items-center shrink-0">
        <div className="relative w-full sm:w-auto">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search records..."
            className="pl-8 w-full sm:w-[300px]"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={!isEditable}
          />
        </div>
      </div>

      <Card className="flex-1 flex flex-col min-h-0">
        <CardHeader className="shrink-0">
          <CardTitle>PROVISIONAL CASH RECORDS</CardTitle>
          <CardDescription>
            All provisional cash records for {department} Department
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0 flex-1">
          <Table maxHeight="calc(100vh - 300px)" minWidth="1500px">
            <TableHeader>
                <TableRow>
                  <TableHead>VOUCHER ID</TableHead>
                  <TableHead>CLAIMANT</TableHead>
                  <TableHead className="hidden md:table-cell">DESCRIPTION</TableHead>
                  <TableHead>DATE</TableHead>
                  <TableHead>MAIN AMOUNT</TableHead>
                  <TableHead>RETIRED AMOUNT</TableHead>
                  <TableHead>STATUS</TableHead>
                  <TableHead className="hidden md:table-cell">DATE RETIRED</TableHead>
                  <TableHead className="hidden md:table-cell">CLEARED BY</TableHead>
                  <TableHead className="text-right">ACTIONS</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRecords.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} className="h-24 text-center">
                      No records found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRecords.map((record) => {
                    const isEditing = inlineEditingRecord === record.id;

                    return (
                      <TableRow
                        key={record.id}
                        className={`cursor-pointer transition-colors ${isEditing ? '!bg-blue-200 !border-2 !border-blue-500 !text-gray-900' : 'hover:!bg-slate-100 hover:!bg-opacity-50'}`}
                        onClick={() => !isEditing && isEditable && handleStartInlineEdit(record)}
                      >
                        <TableCell className="font-medium">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{record.voucherRef}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{record.voucherRef}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{record.claimant}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{record.claimant}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="hidden md:table-cell max-w-xs truncate">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{record.description}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{record.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{formatVMSDateTime(record.date)}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{formatVMSDateTime(record.date)}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">
                                  {formatNumberWithCommas(record.mainAmount)} {record.currency}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{formatNumberWithCommas(record.mainAmount)} {record.currency}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.001"
                              value={inlineAmountRetired}
                              onChange={(e) => setInlineAmountRetired(e.target.value)}
                              className="w-24 !bg-white !text-gray-900 !border-gray-300"
                              placeholder="Amount"
                              onClick={(e) => e.stopPropagation()}
                            />
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">
                                    {record.amountRetired ? `${formatNumberWithCommas(record.amountRetired)} ${record.currency}` : '—'}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{record.amountRetired ? `${formatNumberWithCommas(record.amountRetired)} ${record.currency}` : '—'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </TableCell>
                        <TableCell>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">
                                  {record.clearanceRemark ? (
                                    <span className={getClearanceClass(record.clearanceRemark)}>
                                      {record.clearanceRemark}
                                      {record.clearanceRemark !== "CLEARED" && record.amountRetired !== undefined ? (
                                        ` (${formatNumberWithCommas(Math.abs(record.mainAmount - record.amountRetired))} ${record.currency})`
                                      ) : ''}
                                    </span>
                                  ) : '—'}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  {record.clearanceRemark ? (
                                    <span className={getClearanceClass(record.clearanceRemark)}>
                                      {record.clearanceRemark}
                                      {record.clearanceRemark !== "CLEARED" && record.amountRetired !== undefined ? (
                                        ` (${formatNumberWithCommas(Math.abs(record.mainAmount - record.amountRetired))} ${record.currency})`
                                      ) : ''}
                                    </span>
                                  ) : '—'}
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{record.dateRetired ? formatVMSDateTime(record.dateRetired) : '—'}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{record.dateRetired ? formatVMSDateTime(record.dateRetired) : '—'}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{record.clearedBy || '—'}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{record.clearedBy || '—'}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="text-right">
                          {isEditing ? (
                            <div className="flex gap-1" onClick={(e) => e.stopPropagation()}>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleSaveInlineEdit(record)}
                                className="text-green-600 hover:text-green-700"
                              >
                                Save
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleCancelInlineEdit}
                                className="text-red-600 hover:text-red-700"
                              >
                                Cancel
                              </Button>
                            </div>
                          ) : (
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                console.log('🔧 EDIT BUTTON CLICKED: isEditButtonEnabled =', isEditButtonEnabled, 'isEditable =', isEditable, 'propIsEditable =', propIsEditable, 'lockIsEditable =', lockIsEditable);
                                handleEditRecord(record);
                              }}
                              title={!isEditButtonEnabled ? "Editing disabled - check permissions" : "Edit Record"}
                              disabled={!isEditButtonEnabled}
                              className={!isEditButtonEnabled ? "opacity-50 cursor-not-allowed" : ""}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
        </CardContent>
      </Card>

      {/* Edit Record Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={() => setEditDialogOpen(false)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Provisional Cash Record</DialogTitle>
            <DialogDescription>
              Update retirement details for voucher {currentRecord?.voucherRef}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="amount-retired">Amount Retired</Label>
              <Input
                id="amount-retired"
                type="number"
                placeholder="Enter amount retired"
                value={amountRetired}
                onChange={(e) => setAmountRetired(e.target.value)}
                step="0.01"
                min="0"
                disabled={!isEditable}
              />
              {currentRecord && amountRetired && !isNaN(parseFloat(amountRetired)) && (
                <div className="text-sm mt-1">
                  <span className="text-muted-foreground">Clearance status: </span>
                  <span className={
                    getClearanceClass(
                      calculateClearanceRemark(
                        currentRecord.mainAmount,
                        parseFloat(amountRetired)
                      ).remark
                    )
                  }>
                    {calculateClearanceRemark(
                      currentRecord.mainAmount,
                      parseFloat(amountRetired)
                    ).remark}
                    {calculateClearanceRemark(
                      currentRecord.mainAmount,
                      parseFloat(amountRetired)
                    ).difference > 0 ? (
                      ` (${calculateClearanceRemark(
                        currentRecord.mainAmount,
                        parseFloat(amountRetired)
                      ).difference.toFixed(2)} ${currentRecord.currency})`
                    ) : ''}
                  </span>
                </div>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="cleared-by">Cleared By (Audit Officer)</Label>
              <Select
                value={clearedBy}
                onValueChange={setClearedBy}
                disabled={!isEditable}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Audit Officer who cleared this" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="" className="uppercase">SELECT PERSON</SelectItem>
                  {departmentUsers.map(user => (
                    <SelectItem key={user.id} value={user.name}>
                      {user.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="comment">Comment</Label>
              <Textarea
                id="comment"
                placeholder="Enter optional comment"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={3}
                disabled={!isEditable}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>Cancel</Button>
            <Button
              onClick={handleSaveEdit}
              disabled={!amountRetired || isNaN(parseFloat(amountRetired)) || !clearedBy || !isEditable}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

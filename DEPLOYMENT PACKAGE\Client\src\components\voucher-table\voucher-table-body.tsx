import React, { useState, useEffect } from 'react';
import { Eye, RefreshCw, Trash2, Save, X, Edit } from 'lucide-react';
import { formatNumberWithCommas, formatVMSDateTime } from '@/utils/formatUtils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableRow,
  TableCell
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Voucher, TransactionStatus } from '@/lib/types';
import { UnifiedVoucherBadges } from '@/components/common/UnifiedVoucherBadges';
import { useAppStore } from '@/lib/store';

interface VoucherTableBodyProps {
  filteredVouchers: Voucher[];
  selectable: boolean;
  selectedVouchers: string[];
  handleSelectVoucher: (voucherId: string) => void;
  handleViewVoucher: (voucher: Voucher) => void;
  handleAddBack: (voucher: Voucher) => void;
  handleDeleteVoucher: (voucherId: string) => void;
  view: string;
  isAudit: boolean;
  showAddBack: boolean;
  showDelete: boolean;
  showPreAuditedBy: boolean;
  // PENDING TAB EDITING: New props for editing functionality
  enablePendingEditing?: boolean;
  voucherEdits?: Record<string, any>;
  onVoucherEdit?: (voucherId: string, field: string, value: any) => void;
  onSaveVoucherEdits?: (voucherId: string) => void;
}

export function VoucherTableBody({
  filteredVouchers,
  selectable,
  selectedVouchers,
  handleSelectVoucher,
  handleViewVoucher,
  handleAddBack,
  handleDeleteVoucher,
  view,
  isAudit,
  showAddBack,
  showDelete,
  showPreAuditedBy,
  // PENDING TAB EDITING: New props for editing functionality
  enablePendingEditing = false,
  voucherEdits = {},
  onVoucherEdit,
  onSaveVoucherEdits
}: VoucherTableBodyProps) {
  // CRITICAL FIX: Real-time status column updates
  const [forceUpdate, setForceUpdate] = useState(0);
  const vouchers = useAppStore((state) => state.vouchers);

  // PENDING TAB EDITING: State management for editing
  const [editingVoucher, setEditingVoucher] = useState<string | null>(null);
  const isPendingView = view === 'pending-submission';
  const canEdit = enablePendingEditing && isPendingView && onVoucherEdit && onSaveVoucherEdits;

  // PENDING TAB EDITING: Helper functions
  const handleStartEditing = (voucherId: string) => {
    if (!canEdit) return;
    setEditingVoucher(voucherId);

    // Initialize edits with current voucher data
    const voucher = filteredVouchers.find(v => v.id === voucherId);
    if (voucher && onVoucherEdit) {
      onVoucherEdit(voucherId, 'claimant', voucher.claimant);
      onVoucherEdit(voucherId, 'description', voucher.description);
      onVoucherEdit(voucherId, 'amount', voucher.amount);
      onVoucherEdit(voucherId, 'currency', voucher.currency);
    }
  };

  const handleCancelEditing = (voucherId: string) => {
    // PENDING TAB EDITING FIX: Clear all edits when canceling
    if (onVoucherEdit) {
      // Clear all edited fields by setting them to empty/original values
      const originalVoucher = filteredVouchers.find(v => v.id === voucherId);
      if (originalVoucher) {
        onVoucherEdit(voucherId, 'claimant', originalVoucher.claimant);
        onVoucherEdit(voucherId, 'description', originalVoucher.description);
        onVoucherEdit(voucherId, 'amount', originalVoucher.amount);
        onVoucherEdit(voucherId, 'currency', originalVoucher.currency);
      }
    }
    setEditingVoucher(null);
  };

  const handleSaveEditing = (voucherId: string) => {
    if (onSaveVoucherEdits) {
      onSaveVoucherEdits(voucherId);
    }
    setEditingVoucher(null);
  };

  // Listen for real-time voucher updates to force status column re-renders
  useEffect(() => {
    const handleVoucherUpdate = (event: CustomEvent) => {
      console.log('🔄 REAL-TIME STATUS: Voucher table received update event:', event.detail?.type);
      // Force re-render to update status columns
      setForceUpdate(prev => prev + 1);
    };

    const handleVoucherListRefresh = (event: CustomEvent) => {
      console.log('🔄 REAL-TIME CREATION: Voucher table received list refresh event:', event.detail?.type);
      // Force re-render for new voucher creation
      setForceUpdate(prev => prev + 1);
    };

    // Listen for both WebSocket updates and custom events
    window.addEventListener('voucherUpdated', handleVoucherUpdate as EventListener);
    window.addEventListener('voucherListRefresh', handleVoucherListRefresh as EventListener);

    return () => {
      window.removeEventListener('voucherUpdated', handleVoucherUpdate as EventListener);
      window.removeEventListener('voucherListRefresh', handleVoucherListRefresh as EventListener);
    };
  }, []);

  // Get fresh voucher data from store for real-time status updates
  const getLatestVoucherData = (voucherId: string) => {
    return vouchers.find(v => v.id === voucherId) || filteredVouchers.find(v => v.id === voucherId);
  };
  const getStatusBadge = (status: TransactionStatus) => {
    switch (status) {
      case 'VOUCHER CERTIFIED':
        return <Badge className="bg-green-500 text-white">CERTIFIED</Badge>;
      case 'VOUCHER REJECTED':
        return <Badge className="bg-red-500 text-white">REJECTED</Badge>;
      case 'AUDIT: PROCESSING':
        return <Badge className="bg-yellow-500 text-black">RECEIVED: PROCESSING</Badge>;
      case 'PENDING RECEIPT':
        return <Badge className="bg-yellow-500 text-black">PENDING RECEIPT</Badge>;
      case 'RE-SUBMISSION':
        return <Badge className="bg-blue-500 text-white">RE-SUBMISSION</Badge>;
      case 'PENDING SUBMISSION':
        return <Badge className="border border-gray-200">PENDING SUBMISSION</Badge>;
      case 'VOUCHER RETURNED':
        return <Badge className="bg-gray-500 text-white">RETURNED</Badge>;
      default:
        return <Badge className="border border-gray-200">{status}</Badge>;
    }
  };

  // REMOVED: Legacy badge logic replaced by unified system

  const isVoucherSelectable = (voucher: Voucher): boolean => {
    return (voucher.status === "PENDING" || voucher.status === "PENDING SUBMISSION") && !voucher.sentToAudit;
  };

  // Helper function to get comment text safely
  const getCommentText = (voucher: Voucher): string => {
    // PRODUCTION FIX: Comprehensive comment resolution

    // For returned or pending return vouchers, prioritize return-specific comments
    if (voucher.pendingReturn || voucher.isReturned || voucher.status === "VOUCHER RETURNED") {
      // Check return comment first
      if (voucher.returnComment &&
          voucher.returnComment !== "NO COMMENT PROVIDED" &&
          voucher.returnComment !== "null" &&
          voucher.returnComment.trim() !== "") {
        return String(voucher.returnComment);
      }

      // Check original return reason
      if (voucher.original_return_reason &&
          voucher.original_return_reason !== "NO COMMENT PROVIDED" &&
          voucher.original_return_reason !== "null") {
        return String(voucher.original_return_reason);
      }
    }

    // For resubmitted vouchers, check original rejection reason
    if (voucher.isResubmitted || voucher.is_resubmitted) {
      if (voucher.original_rejection_reason &&
          voucher.original_rejection_reason !== "NO COMMENT PROVIDED" &&
          voucher.original_rejection_reason !== "null") {
        return String(voucher.original_rejection_reason);
      }
    }

    // Check regular comment field (filter out generic messages)
    if (voucher.comment &&
        voucher.comment !== "NO COMMENT PROVIDED" &&
        voucher.comment !== "null" &&
        !voucher.comment.includes("Re-added from rejection") &&
        !voucher.comment.includes("Re-added from returned") &&
        voucher.comment.trim() !== "") {
      return String(voucher.comment);
    }

    // Last resort
    return "NO COMMENT PROVIDED";
  };

  return (
    <div className="w-full">
      <table className="w-full table-fixed" style={{ tableLayout: 'fixed' }}>
        <tbody>
        {filteredVouchers.length === 0 ? (
          <tr className="border-b h-14">
            <td colSpan={selectable ? 9 : 8} className="p-4 text-center uppercase font-medium">
              No vouchers found.
            </td>
          </tr>
        ) : (
          filteredVouchers.map((voucher) => {
            const isSelectableVoucher = selectable && isVoucherSelectable(voucher);

            return (
              <tr key={voucher.id} className="border-b h-14">
                {selectable && (
                  <td className="sticky left-0 bg-background z-10 p-4 align-middle w-[5%] text-center">
                    <Checkbox
                      checked={selectedVouchers.includes(voucher.id)}
                      onCheckedChange={() => handleSelectVoucher(voucher.id)}
                      aria-label={`Select voucher ${voucher.voucherId}`}
                      disabled={!isSelectableVoucher}
                    />
                  </td>
                )}
                <td className="font-medium uppercase sticky left-0 bg-background z-10 p-4 align-middle w-[15%] text-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="block truncate font-mono">
                          {voucher.voucherId || (voucher as any).voucher_id || 'N/A'}
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{voucher.voucherId || (voucher as any).voucher_id || 'N/A'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </td>
                <td className="uppercase p-4 align-middle w-[20%] text-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="block truncate">{formatVMSDateTime(voucher.date)}</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <p className="font-semibold">Voucher Date</p>
                          <p>{formatVMSDateTime(voucher.date)}</p>
                          {voucher.createdAt && (
                            <>
                              <p className="font-semibold mt-2">Created</p>
                              <p>{formatVMSDateTime(voucher.createdAt)}</p>
                            </>
                          )}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </td>
                <td className="uppercase p-4 align-middle w-[20%] text-center">
                  {canEdit && editingVoucher === voucher.id ? (
                    <Input
                      value={voucherEdits[voucher.id]?.claimant || voucher.claimant}
                      onChange={(e) => onVoucherEdit!(voucher.id, 'claimant', e.target.value)}
                      className="text-center text-xs"
                      placeholder="Enter claimant name"
                    />
                  ) : (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="block truncate">{voucher.claimant}</span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{voucher.claimant}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </td>
                <td className="max-w-xs truncate uppercase p-4 align-middle w-[25%] text-center">
                  {canEdit && editingVoucher === voucher.id ? (
                    <Input
                      value={voucherEdits[voucher.id]?.description || voucher.description}
                      onChange={(e) => onVoucherEdit!(voucher.id, 'description', e.target.value)}
                      className="text-center text-xs"
                      placeholder="Enter description"
                    />
                  ) : (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="block truncate">{voucher.description}</span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{voucher.description}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </td>
                <td className="uppercase p-4 align-middle w-[10%] text-center text-xs">
                  {canEdit && editingVoucher === voucher.id ? (
                    <div className="flex flex-col gap-1">
                      <Input
                        type="number"
                        value={voucherEdits[voucher.id]?.amount || voucher.amount}
                        onChange={(e) => onVoucherEdit!(voucher.id, 'amount', parseFloat(e.target.value) || 0)}
                        className="text-center text-xs h-6"
                        placeholder="Amount"
                        step="0.01"
                        min="0"
                      />
                      <Select
                        value={voucherEdits[voucher.id]?.currency || voucher.currency}
                        onValueChange={(value) => onVoucherEdit!(voucher.id, 'currency', value)}
                      >
                        <SelectTrigger className="text-center text-xs h-6">
                          <SelectValue placeholder="Currency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="GHS">GHS</SelectItem>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="GBP">GBP</SelectItem>
                          <SelectItem value="CFA">CFA</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  ) : (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="block truncate">
                            {formatNumberWithCommas(voucher.amount)} {voucher.currency}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{formatNumberWithCommas(voucher.amount)} {voucher.currency}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </td>

                {!isAudit && view === 'certified' && (
                  <td className="uppercase p-4 align-middle w-[15%] text-center text-xs">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="block truncate">
                            {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </td>
                )}

                {isAudit && showPreAuditedBy && (
                  <>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.preAuditedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.preAuditedBy || '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                  </>
                )}

                {view === 'dispatched' && (
                  <>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.certifiedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.certifiedBy || '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.taxType && voucher.taxAmount ?
                                `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}` :
                                (voucher.taxType || '-')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {voucher.taxType && voucher.taxAmount ?
                                `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}` :
                                (voucher.taxType || '-')}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.auditDispatchedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.auditDispatchedBy || '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.auditDispatchTime ? formatVMSDateTime(voucher.auditDispatchTime) : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Audit Dispatch Time</p>
                              <p>{voucher.auditDispatchTime ? formatVMSDateTime(voucher.auditDispatchTime) : 'Not dispatched'}</p>
                              {voucher.auditDispatchedBy && (
                                <>
                                  <p className="font-semibold mt-2">Dispatched By</p>
                                  <p>{voucher.auditDispatchedBy}</p>
                                </>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.receivedBy ?
                                `${voucher.receivedBy}${voucher.departmentReceiptTime ? ` AT ${formatVMSTime(voucher.departmentReceiptTime)}` : ''}` :
                                'PENDING'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Department Receipt</p>
                              {voucher.receivedBy ? (
                                <>
                                  <p className="font-semibold mt-2">Received By</p>
                                  <p>{voucher.receivedBy}</p>
                                  {voucher.departmentReceiptTime && (
                                    <>
                                      <p className="font-semibold mt-2">Receipt Time</p>
                                      <p>{formatVMSDateTime(voucher.departmentReceiptTime)}</p>
                                    </>
                                  )}
                                </>
                              ) : (
                                <p>Pending receipt</p>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                  </>
                )}

                {view === 'returned' && (
                  <>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.returnTime ? formatVMSDateTime(voucher.returnTime) : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Return Time</p>
                              <p>{voucher.returnTime ? formatVMSDateTime(voucher.returnTime) : 'Not returned'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <div className="flex items-center justify-center">
                        {/* CRITICAL FIX: Use latest voucher data for real-time status updates */}
                        {(() => {
                          const latestVoucher = getLatestVoucherData(voucher.id);
                          return getStatusBadge(latestVoucher?.status || voucher.status);
                        })()}
                        {/* RESUBMISSION BADGE REMOVED: Handled in the main badge section below to avoid duplicates */}
                      </div>
                    </td>
                    <td className="uppercase max-w-[250px] truncate p-4 align-middle w-[25%] text-center">
                      {getCommentText(voucher)}
                    </td>
                  </>
                )}

                {view === 'rejected' && (
                  <td className="uppercase p-4 align-middle w-[15%] text-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="block truncate">
                            {voucher.rejectionTime ? formatVMSDateTime(voucher.rejectionTime) : '-'}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-center">
                            <p className="font-semibold">Rejection Time</p>
                            <p>{voucher.rejectionTime ? formatVMSDateTime(voucher.rejectionTime) : 'Not rejected'}</p>
                            {voucher.rejectedBy && (
                              <>
                                <p className="font-semibold mt-2">Rejected By</p>
                                <p>{voucher.rejectedBy}</p>
                              </>
                            )}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </td>
                )}

                {isAudit && view === 'rejected' && (
                  <>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.dispatchedBy || '-'}</td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.dispatchToAuditBy || '-'}</td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.rejectedBy || '-'}</td>
                  </>
                )}

                {view !== 'rejected' && view !== 'returned' && (
                  <td className="p-4 align-middle w-[15%] text-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center justify-center gap-2">
                            {/* CRITICAL FIX: Use latest voucher data for real-time status updates */}
                            {(() => {
                              const latestVoucher = getLatestVoucherData(voucher.id);
                              return getStatusBadge(latestVoucher?.status || voucher.status);
                            })()}
                            {/* PRODUCTION FIX: Unified badge system with tab context - also use latest data */}
                            <UnifiedVoucherBadges
                              voucher={getLatestVoucherData(voucher.id) || voucher}
                              tabName={view}
                              size="sm"
                              className="flex-wrap"
                            />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{getLatestVoucherData(voucher.id)?.status || voucher.status}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </td>
                )}

                <td className="text-center sticky right-0 bg-background z-10 p-4 align-middle w-[10%]">
                  <div className="flex justify-center gap-2">
                    <Button
                      className="hover:bg-gray-100 h-8 w-8 p-0 bg-blue-50"
                      onClick={(e) => {
                        e.stopPropagation();
                        console.log('Eye button clicked for voucher:', voucher.id);
                        handleViewVoucher(voucher);
                      }}
                      title="View Voucher Details"
                    >
                      <Eye className="h-4 w-4 text-blue-600" />
                    </Button>

                    {/* PENDING TAB EDITING: Edit/Save/Cancel buttons */}
                    {canEdit && (
                      <>
                        {editingVoucher === voucher.id ? (
                          <>
                            <Button
                              className="hover:bg-green-100 h-8 w-8 p-0 bg-green-50"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSaveEditing(voucher.id);
                              }}
                              title="Save Changes"
                            >
                              <Save className="h-4 w-4 text-green-600" />
                            </Button>
                            <Button
                              className="hover:bg-red-100 h-8 w-8 p-0 bg-red-50"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCancelEditing(voucher.id);
                              }}
                              title="Cancel Editing"
                            >
                              <X className="h-4 w-4 text-red-600" />
                            </Button>
                          </>
                        ) : (
                          <Button
                            className="hover:bg-blue-100 h-8 w-8 p-0 bg-blue-50"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleStartEditing(voucher.id);
                            }}
                            title="Edit Voucher"
                          >
                            <Edit className="h-4 w-4 text-blue-600" />
                          </Button>
                        )}
                      </>
                    )}

                    {showAddBack && (
                      <Button
                        className="hover:bg-gray-100 h-8 w-8 p-0"
                        onClick={() => handleAddBack(voucher)}
                        title="Add Back to Pending Submission"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    )}

                    {(showDelete || (view === 'pending-submission' && !voucher.sentToAudit)) && (
                      <Button
                        className="text-red-500 hover:text-red-700 hover:bg-gray-100 h-8 w-8 p-0"
                        onClick={() => handleDeleteVoucher(voucher.id)}
                        title="Delete Voucher"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </td>
              </tr>
            );
          })
        )}
        {/* Add extra padding space at the bottom */}
        <tr className="border-b">
          <td colSpan={12} className="h-24 py-8"></td>
        </tr>
        </tbody>
      </table>
    </div>
  );
}

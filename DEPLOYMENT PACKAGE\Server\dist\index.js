"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const http_1 = __importDefault(require("http"));
const path_1 = __importDefault(require("path"));
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const morgan_1 = __importDefault(require("morgan"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const dotenv_1 = __importDefault(require("dotenv"));
const db_1 = require("./database/db");
const db_2 = __importDefault(require("./database/db"));
const DatabaseManager_1 = require("./database/DatabaseManager");
const logger_1 = require("./utils/logger");
const index_1 = require("./routes/index");
const socketHandlers_1 = require("./socket/socketHandlers");
const ServiceDiscovery_1 = require("./utils/ServiceDiscovery");
const workflow_service_1 = require("./services/workflow-service");
const file_storage_1 = require("./utils/file-storage");
const backup_scheduler_1 = require("./services/backup-scheduler");
const live_time_sync_1 = require("./services/live-time-sync");
dotenv_1.default.config();
const app = (0, express_1.default)();
const PREFERRED_PORT = parseInt(process.env.PREFERRED_PORT || process.env.PORT || '8080');
const NODE_ENV = process.env.NODE_ENV || 'development';
const corsOrigins = '*';
app.use((0, cors_1.default)({
    origin: corsOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Cache-Control',
        'Pragma',
        'Expires',
        'Accept',
        'Accept-Language',
        'Accept-Encoding'
    ],
    exposedHeaders: ['Set-Cookie'],
    optionsSuccessStatus: 200,
    maxAge: 86400
}));
app.use((req, res, next) => {
    res.removeHeader('Strict-Transport-Security');
    res.removeHeader('Content-Security-Policy');
    res.removeHeader('Upgrade-Insecure-Requests');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'SAMEORIGIN');
    next();
});
app.use((0, morgan_1.default)(NODE_ENV === 'production' ? 'combined' : 'dev', {
    stream: { write: (message) => logger_1.logger.info(message.trim()) }
}));
app.use(express_1.default.json({
    limit: '10mb',
    verify: (req, res, buf) => {
        if (buf.length > 10 * 1024 * 1024) {
            throw new Error('Request entity too large');
        }
    }
}));
app.use(express_1.default.urlencoded({
    extended: true,
    limit: '10mb',
    parameterLimit: 1000
}));
app.use((0, cookie_parser_1.default)());
app.use((req, res, next) => {
    req.startTime = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - req.startTime;
        if (duration > 5000) {
            logger_1.logger.warn(`Slow request: ${req.method} ${req.url} took ${duration}ms`);
        }
    });
    next();
});
app.use(express_1.default.static(path_1.default.join(__dirname, '../public'), {
    maxAge: '1d',
    etag: true,
    lastModified: true,
    index: ['index.html'],
    setHeaders: (res, path, stat) => {
        res.removeHeader('Strict-Transport-Security');
        res.removeHeader('Upgrade-Insecure-Requests');
        if (path.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
        }
        else if (path.endsWith('.css')) {
            res.setHeader('Content-Type', 'text/css; charset=utf-8');
        }
        else if (path.endsWith('.html')) {
            res.setHeader('Content-Type', 'text/html; charset=utf-8');
            res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        }
        res.setHeader('X-Content-Type-Options', 'nosniff');
    }
}));
app.use('/api', (req, res, next) => {
    logger_1.logger.info(`API Request: ${req.method} ${req.url}`, {
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        timestamp: new Date().toISOString()
    });
    next();
}, index_1.apiRouter);
app.get('/health', (req, res) => {
    const healthData = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '5.0.0',
        environment: NODE_ENV,
        pid: process.pid,
        platform: process.platform,
        nodeVersion: process.version
    };
    res.json(healthData);
});
app.get('/health/detailed', async (req, res) => {
    try {
        const healthData = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: '5.0.0',
            environment: NODE_ENV,
            database: {
                connected: true,
                lastCheck: new Date().toISOString()
            },
            websocket: {
                enabled: true,
                connections: 0
            },
            system: {
                pid: process.pid,
                platform: process.platform,
                nodeVersion: process.version,
                cpuUsage: process.cpuUsage()
            }
        };
        res.json(healthData);
    }
    catch (error) {
        res.status(500).json({
            status: 'unhealthy',
            error: error?.message || 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});
app.use((err, req, res, next) => {
    logger_1.logger.error('Server error occurred:', {
        message: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
    });
    const statusCode = err.status || err.statusCode || 500;
    const errorMessage = NODE_ENV === 'production'
        ? 'Internal Server Error'
        : err.message || 'Internal Server Error';
    const errorResponse = {
        error: errorMessage,
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown',
        ...(NODE_ENV !== 'production' && { stack: err.stack })
    };
    res.status(statusCode).json(errorResponse);
});
app.get('*', (req, res) => {
    if (req.path.startsWith('/api')) {
        return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.sendFile(path_1.default.join(__dirname, '../public/index.html'), (err) => {
        if (err) {
            logger_1.logger.error('Error serving index.html:', err);
            res.status(500).json({ error: 'Failed to serve application' });
        }
    });
});
const server = http_1.default.createServer(app);
async function startServer() {
    try {
        logger_1.logger.info('🚀 Starting VMS Server - Production Grade Architecture...');
        const PORT = PREFERRED_PORT;
        logger_1.logger.info(`🎯 Using forced port: ${PORT}`);
        const io = new socket_io_1.Server(server, {
            cors: {
                origin: true,
                methods: ['GET', 'POST'],
                credentials: true
            },
            pingTimeout: 60000,
            pingInterval: 25000,
            connectTimeout: 45000,
            transports: ['polling', 'websocket'],
            allowEIO3: true,
            path: '/socket.io',
            serveClient: false,
            maxHttpBufferSize: 1e6,
            httpCompression: true,
            perMessageDeflate: {
                threshold: 1024,
                concurrencyLimit: 10,
                windowBits: 13
            },
            destroyUpgrade: false,
            destroyUpgradeTimeout: 1000,
            connectionStateRecovery: {
                maxDisconnectionDuration: 2 * 60 * 1000,
                skipMiddlewares: false,
            },
            adapter: undefined
        });
        (0, socketHandlers_1.setIoInstance)(io);
        (0, socketHandlers_1.setupSocketHandlers)(io);
        logger_1.logger.info('✅ Single WebSocket system initialized - No duplicates');
        logger_1.logger.info('🎯 Duplicate broadcast prevention: Only socketHandlers active');
        let databaseAvailable = false;
        let retryCount = 0;
        const maxRetries = 5;
        const baseDelay = 2000;
        logger_1.logger.info('🔄 Initializing database connection with enterprise retry logic...');
        while (!databaseAvailable && retryCount < maxRetries) {
            try {
                await (0, db_1.initializeDatabase)();
                logger_1.logger.info('✅ Database connected successfully - Production ready');
                databaseAvailable = true;
                logger_1.logger.info('🔍 Testing database connectivity...');
                logger_1.logger.info('🔄 Initializing workflow service...');
                await (0, workflow_service_1.initializeWorkflowService)(db_2.default, io);
                logger_1.logger.info('✅ Workflow service initialized successfully');
                logger_1.logger.info('🔄 Initializing file storage...');
                (0, file_storage_1.initializeStorage)();
                logger_1.logger.info('✅ File storage initialized successfully');
            }
            catch (error) {
                retryCount++;
                const delay = baseDelay * Math.pow(2, retryCount - 1);
                logger_1.logger.warn(`⚠️  Database connection attempt ${retryCount}/${maxRetries} failed:`, {
                    error: error.message,
                    code: error.code,
                    errno: error.errno,
                    sqlState: error.sqlState
                });
                if (retryCount < maxRetries) {
                    logger_1.logger.info(`🔄 Retrying database connection in ${delay / 1000} seconds...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                else {
                    logger_1.logger.error('❌ Database connection failed after all retries.');
                    logger_1.logger.error('🔧 Server will continue in degraded mode - WebSocket functionality available');
                    logger_1.logger.error('💡 Please check database configuration and connectivity');
                }
            }
        }
        if (databaseAvailable) {
            logger_1.logger.info('📊 Database health monitoring DISABLED to prevent infinite loops');
        }
        server.listen(PORT, '0.0.0.0', async () => {
            logger_1.logger.info('🎉 VMS Server - Production Grade Startup Complete!');
            logger_1.logger.info('='.repeat(60));
            logger_1.logger.info(`🚀 Server Status: RUNNING (Port ${PORT})`);
            logger_1.logger.info(`🔌 WebSocket Status: ACTIVE (Enterprise Configuration)`);
            logger_1.logger.info(`💾 Database Status: ${databaseAvailable ? 'CONNECTED' : 'DEGRADED MODE'}`);
            logger_1.logger.info(`🌐 Environment: ${NODE_ENV.toUpperCase()}`);
            logger_1.logger.info(`📊 Process ID: ${process.pid}`);
            logger_1.logger.info('='.repeat(60));
            try {
                logger_1.logger.info('🌐 Initializing Hybrid Network System...');
                const { networkIntegrationService } = require('./services/network-integration-service');
                await networkIntegrationService.initialize();
                logger_1.logger.info('✅ Hybrid Network System initialized successfully');
                logger_1.logger.info(`🎛️ VMS-ADMIN Dashboard: http://localhost:8081`);
            }
            catch (networkError) {
                logger_1.logger.error('❌ Failed to initialize Hybrid Network System:', networkError);
                logger_1.logger.warn('⚠️ VMS will continue without advanced network features');
            }
            if (databaseAvailable) {
                try {
                    const dbManager = new DatabaseManager_1.DatabaseManager();
                    await dbManager.initialize();
                    dbManager.logPoolHealth();
                    setInterval(() => {
                        try {
                            dbManager.logPoolHealth();
                        }
                        catch (error) {
                            logger_1.logger.error('❌ Error monitoring DB pool health:', error);
                        }
                    }, 300000);
                }
                catch (error) {
                    logger_1.logger.warn('⚠️ Could not initialize DB monitoring:', error);
                }
            }
            logger_1.logger.info('📡 Service Endpoints:');
            logger_1.logger.info(`   🌐 WebSocket: http://localhost:${PORT}/socket.io/`);
            logger_1.logger.info(`   📊 Health Check: http://localhost:${PORT}/health`);
            logger_1.logger.info(`   📈 Detailed Health: http://localhost:${PORT}/health/detailed`);
            logger_1.logger.info(`   🔧 API Base: http://localhost:${PORT}/api`);
            if (databaseAvailable) {
                try {
                    const { scheduleSessionCleanup } = await Promise.resolve().then(() => __importStar(require('./tasks/session-cleanup.js')));
                    setInterval(async () => {
                        try {
                            const { cleanupStaleSessions } = await Promise.resolve().then(() => __importStar(require('./tasks/session-cleanup.js')));
                            await cleanupStaleSessions();
                        }
                        catch (cleanupError) {
                            logger_1.logger.error('Session cleanup error:', cleanupError);
                        }
                    }, 10 * 60 * 1000);
                    logger_1.logger.info('✅ Session cleanup scheduled (every 10 minutes)');
                    setTimeout(async () => {
                        try {
                            const { cleanupStaleSessions } = await Promise.resolve().then(() => __importStar(require('./tasks/session-cleanup.js')));
                            await cleanupStaleSessions();
                            logger_1.logger.info('✅ Initial session cleanup completed');
                        }
                        catch (cleanupError) {
                            logger_1.logger.error('Initial session cleanup error:', cleanupError);
                        }
                    }, 30000);
                }
                catch (error) {
                    logger_1.logger.warn('⚠️  Task scheduling failed:', error);
                }
                try {
                    backup_scheduler_1.backupScheduler.scheduleDaily('02:00', 30);
                    logger_1.logger.info('✅ Automated backup scheduler started (daily at 2:00 AM ±30min)');
                }
                catch (error) {
                    logger_1.logger.warn('⚠️ Backup scheduler failed to start:', error);
                }
                try {
                    live_time_sync_1.liveTimeSyncService.start();
                    logger_1.logger.info('✅ Live time synchronization service started (LAN-optimized)');
                }
                catch (error) {
                    logger_1.logger.warn('⚠️ Live time sync service failed to start:', error);
                }
            }
            try {
                const os = await Promise.resolve().then(() => __importStar(require('os')));
                const networkInterfaces = os.networkInterfaces();
                const lanIPs = Object.values(networkInterfaces)
                    .flat()
                    .filter((iface) => iface.family === 'IPv4' && !iface.internal)
                    .map((iface) => iface.address);
                if (lanIPs.length > 0) {
                    logger_1.logger.info('🌍 Enterprise Network Access:');
                    lanIPs.forEach(ip => {
                        logger_1.logger.info(`   🖥️  VMS System: http://${ip}:${PORT}`);
                        logger_1.logger.info(`   🔌 WebSocket: http://${ip}:${PORT}/socket.io/`);
                    });
                }
                const memUsage = process.memoryUsage();
                logger_1.logger.info('📊 System Resources:');
                logger_1.logger.info(`   💾 Memory: ${Math.round(memUsage.rss / 1024 / 1024)}MB RSS`);
                logger_1.logger.info(`   🔄 Uptime: ${Math.round(process.uptime())}s`);
            }
            catch (error) {
                logger_1.logger.warn('Could not determine network configuration:', error);
            }
            try {
                await ServiceDiscovery_1.serviceDiscovery.startAnnouncement(PORT);
                logger_1.logger.info('📡 Service Discovery: ACTIVE - Clients can auto-discover this server');
            }
            catch (error) {
                logger_1.logger.warn('⚠️  Service discovery failed to start:', error);
            }
            logger_1.logger.info('='.repeat(60));
            logger_1.logger.info('🎯 VMS Server - Ready for Production Operations!');
            logger_1.logger.info('='.repeat(60));
        });
    }
    catch (error) {
        logger_1.logger.error('❌ CRITICAL: Server startup failed:', {
            message: error?.message || 'Unknown error',
            stack: error?.stack || 'No stack trace',
            timestamp: new Date().toISOString()
        });
        logger_1.logger.info('🔄 Attempting graceful degradation...');
        try {
            const fallbackPort = PREFERRED_PORT;
            server.listen(fallbackPort, '0.0.0.0', () => {
                logger_1.logger.warn('⚠️  Server started in MINIMAL MODE');
                logger_1.logger.warn('🔧 Limited functionality - please check configuration');
                logger_1.logger.info(`📊 Health endpoint available: http://localhost:${fallbackPort}/health`);
            });
        }
        catch (degradedError) {
            logger_1.logger.error('❌ FATAL: Complete startup failure:', degradedError);
            process.exit(1);
        }
    }
}
process.on('uncaughtException', (error) => {
    logger_1.logger.error('🚨 CRITICAL: Uncaught Exception detected:', {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        pid: process.pid
    });
    logger_1.logger.info('🔄 Attempting graceful recovery...');
    setTimeout(() => {
        logger_1.logger.error('💥 FATAL: Recovery failed, exiting...');
        process.exit(1);
    }, 5000);
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('🚨 CRITICAL: Unhandled Promise Rejection:', {
        reason: reason,
        promise: promise,
        timestamp: new Date().toISOString(),
        pid: process.pid
    });
    logger_1.logger.warn('⚠️  Continuing operation - monitoring for stability');
});
function gracefulShutdown(signal) {
    logger_1.logger.info(`🛑 ${signal} received - Initiating graceful shutdown...`);
    logger_1.logger.info('📊 Shutdown sequence started');
    server.close(async () => {
        logger_1.logger.info('✅ HTTP server closed');
        try {
            logger_1.logger.info('🧹 Performing cleanup tasks...');
            logger_1.logger.info('💾 Closing database connections...');
            logger_1.logger.info('🔌 Closing WebSocket connections...');
            try {
                await ServiceDiscovery_1.serviceDiscovery.stopAnnouncement();
                logger_1.logger.info('📡 Service discovery stopped');
            }
            catch (error) {
                logger_1.logger.warn('⚠️  Error stopping service discovery:', error);
            }
            logger_1.logger.info('✅ Graceful shutdown completed successfully');
            process.exit(0);
        }
        catch (error) {
            logger_1.logger.error('❌ Error during shutdown cleanup:', error);
            process.exit(1);
        }
    });
    setTimeout(() => {
        logger_1.logger.error('⏰ Shutdown timeout exceeded - forcing exit');
        process.exit(1);
    }, 30000);
}
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
startServer();

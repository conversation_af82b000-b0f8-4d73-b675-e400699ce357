"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.help = exports.run = void 0;
var fs_1 = require("fs");
var path_1 = require("path");
var bs_logger_1 = require("bs-logger");
var fast_json_stable_stringify_1 = __importDefault(require("fast-json-stable-stringify"));
var json5_1 = require("json5");
var create_jest_preset_1 = require("../../presets/create-jest-preset");
var backports_1 = require("../../utils/backports");
var presets_1 = require("../helpers/presets");
var migrateGlobalConfigToTransformConfig = function (transformConfig, globalsTsJestConfig) {
    if (transformConfig) {
        return Object.entries(transformConfig).reduce(function (previousValue, currentValue) {
            var _a, _b;
            var _c = __read(currentValue, 2), key = _c[0], transformOptions = _c[1];
            if (typeof transformOptions === 'string' && transformOptions.includes('ts-jest')) {
                return __assign(__assign({}, previousValue), (_a = {}, _a[key] = globalsTsJestConfig ? ['ts-jest', globalsTsJestConfig] : 'ts-jest', _a));
            }
            return __assign(__assign({}, previousValue), (_b = {}, _b[key] = transformOptions, _b));
        }, {});
    }
    return {};
};
var migratePresetToTransformConfig = function (transformConfig, preset, globalsTsJestConfig) {
    if (preset) {
        var transformConfigFromPreset = preset.name === "ts-jest/presets/js-with-ts" /* JestPresetNames.jsWithTs */
            ? (0, create_jest_preset_1.createJsWithTsPreset)(globalsTsJestConfig)
            : preset.name === "ts-jest/presets/js-with-babel" /* JestPresetNames.jsWIthBabel */
                ? (0, create_jest_preset_1.createJsWithBabelPreset)(globalsTsJestConfig)
                : (0, create_jest_preset_1.createDefaultPreset)(globalsTsJestConfig);
        return __assign(__assign({}, transformConfig), transformConfigFromPreset.transform);
    }
    return transformConfig;
};
/**
 * @internal
 */
var run = function (args /* , logger: Logger*/) { return __awaiter(void 0, void 0, void 0, function () {
    var nullLogger, file, filePath, name, isPackage, actualConfig, migratedConfig, preset, presetValue, migratedValue, presetValue, migratedValue, globalsTsJestConfig, before, after, stringify, prefix;
    var _a, _b, _c, _d, _e, _f, _g, _h;
    return __generator(this, function (_j) {
        nullLogger = (0, bs_logger_1.createLogger)({ targets: [] });
        file = (_a = args._[0]) === null || _a === void 0 ? void 0 : _a.toString();
        filePath = (0, path_1.resolve)(process.cwd(), file);
        if (!(0, fs_1.existsSync)(filePath)) {
            throw new Error("Configuration file ".concat(file, " does not exists."));
        }
        name = (0, path_1.basename)(file);
        isPackage = name === 'package.json';
        if (!/\.(js|json)$/.test(name)) {
            throw new TypeError("Configuration file ".concat(file, " must be a JavaScript or JSON file."));
        }
        actualConfig = require(filePath);
        if (isPackage) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            actualConfig = actualConfig.jest;
        }
        if (!actualConfig)
            actualConfig = {};
        migratedConfig = (0, backports_1.backportJestConfig)(nullLogger, actualConfig);
        if (migratedConfig.preset) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            preset = (_b = presets_1.allPresets[migratedConfig.preset]) !== null && _b !== void 0 ? _b : presets_1.allPresets["ts-jest/presets/default" /* JestPresetNames.default */];
        }
        else {
            if (args.js) {
                preset = args.js === 'babel' ? presets_1.allPresets["ts-jest/presets/js-with-babel" /* JestPresetNames.jsWIthBabel */] : presets_1.allPresets["ts-jest/presets/js-with-ts" /* JestPresetNames.jsWithTs */];
            }
            else {
                preset = presets_1.allPresets["ts-jest/presets/default" /* JestPresetNames.default */];
            }
        }
        // check the extensions
        if (((_c = migratedConfig.moduleFileExtensions) === null || _c === void 0 ? void 0 : _c.length) && preset) {
            presetValue = dedupSort((_d = preset.value.moduleFileExtensions) !== null && _d !== void 0 ? _d : []).join('::');
            migratedValue = dedupSort(migratedConfig.moduleFileExtensions).join('::');
            if (presetValue === migratedValue) {
                delete migratedConfig.moduleFileExtensions;
            }
        }
        // there is a testRegex, remove our testMatch
        if (typeof migratedConfig.testRegex === 'string' || ((_e = migratedConfig.testRegex) === null || _e === void 0 ? void 0 : _e.length)) {
            delete migratedConfig.testMatch;
        }
        // check the testMatch
        else if (((_f = migratedConfig.testMatch) === null || _f === void 0 ? void 0 : _f.length) && preset) {
            presetValue = dedupSort((_g = preset.value.testMatch) !== null && _g !== void 0 ? _g : []).join('::');
            migratedValue = dedupSort(migratedConfig.testMatch).join('::');
            if (presetValue === migratedValue) {
                delete migratedConfig.testMatch;
            }
        }
        globalsTsJestConfig = (_h = migratedConfig.globals) === null || _h === void 0 ? void 0 : _h['ts-jest'];
        migratedConfig.transform = migrateGlobalConfigToTransformConfig(migratedConfig.transform, globalsTsJestConfig);
        migratedConfig.transform = migratePresetToTransformConfig(migratedConfig.transform, preset, globalsTsJestConfig);
        cleanupConfig(actualConfig);
        cleanupConfig(migratedConfig);
        before = (0, fast_json_stable_stringify_1.default)(actualConfig);
        after = (0, fast_json_stable_stringify_1.default)(migratedConfig);
        if (after === before) {
            process.stderr.write("\nNo migration needed for given Jest configuration\n    ");
            return [2 /*return*/];
        }
        stringify = file.endsWith('.json') ? JSON.stringify : json5_1.stringify;
        prefix = file.endsWith('.json') ? '"jest": ' : 'module.exports = ';
        // output new config
        process.stderr.write("\nMigrated Jest configuration:\n");
        process.stdout.write("".concat(prefix).concat(stringify(migratedConfig, undefined, '  '), "\n"));
        return [2 /*return*/];
    });
}); };
exports.run = run;
function cleanupConfig(config) {
    if (config.globals) {
        delete config.globals['ts-jest'];
        if (!Object.keys(config.globals).length) {
            delete config.globals;
        }
    }
    if (config.transform && !Object.keys(config.transform).length) {
        delete config.transform;
    }
    if (config.moduleFileExtensions) {
        config.moduleFileExtensions = dedupSort(config.moduleFileExtensions);
        if (!config.moduleFileExtensions.length)
            delete config.moduleFileExtensions;
    }
    if (config.testMatch) {
        config.testMatch = dedupSort(config.testMatch);
        if (!config.testMatch.length)
            delete config.testMatch;
    }
    delete config.preset;
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function dedupSort(arr) {
    return arr
        .filter(function (s, i, a) { return a.findIndex(function (e) { return s.toString() === e.toString(); }) === i; })
        .sort(function (a, b) { return (a.toString() > b.toString() ? 1 : a.toString() < b.toString() ? -1 : 0); });
}
/**
 * @internal
 */
var help = function () { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        process.stdout.write("\nUsage:\n  ts-jest config:migrate [options] <config-file>\n\nArguments:\n  <config-file>         Can be a js or json Jest config file. If it is a\n                        package.json file, the configuration will be read from\n                        the \"jest\" property.\n\nOptions:\n  --js ts|babel         Process .js files with ts-jest if 'ts' or with\n                        babel-jest if 'babel'\n  --no-jest-preset      Disable the use of Jest presets\n");
        return [2 /*return*/];
    });
}); };
exports.help = help;

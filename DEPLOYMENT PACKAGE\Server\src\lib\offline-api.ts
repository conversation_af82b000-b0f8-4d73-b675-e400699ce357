import { useAppStore } from '@/lib/store';
import { toast } from 'sonner';

/**
 * Offline-Aware API Wrapper
 * 
 * Automatically queues operations when offline and executes when online
 * Provides seamless offline/online experience for users
 */

interface ApiOptions {
  showOfflineToast?: boolean;
  maxRetries?: number;
  priority?: 'low' | 'normal' | 'high';
  allowOffline?: boolean; // PRODUCTION-SAFE: Allow override for safe operations
}

class OfflineAPI {
  private getStore() {
    return useAppStore.getState();
  }

  /**
   * PRODUCTION-SAFE: Check if operation is safe to perform offline
   */
  private isOperationSafeOffline(operationType: string): boolean {
    // SAFE operations (low conflict risk)
    const safeOperations = [
      'CREATE_VOUCHER',        // Safe - new data creation
      'UPDATE_VOUCHER',        // Safe - single user operation
      'CREATE_PROVISIONAL_CASH' // Safe - department-specific
    ];

    // RISKY operations (high conflict risk)
    const riskyOperations = [
      'SEND_TO_AUDIT',         // Risky - coordination needed
      'RECEIVE_BATCH',         // Risky - multi-voucher processing
      'COMPLETE_PROCESSING',   // Risky - workflow state changes
      'REJECT_VOUCHER',        // Risky - affects other users
      'RETURN_VOUCHER'         // Risky - cross-department workflow
    ];

    return safeOperations.includes(operationType);
  }

  /**
   * PRODUCTION-SAFE: STRICT BLOCKING - Prevent risky offline operations
   */
  private enforceOfflineSafety(operationType: string, options: ApiOptions = {}): void {
    const { isOnline } = this.getStore();

    if (isOnline) return; // No restrictions if online

    // Allow override for specific cases (if explicitly allowed)
    if (options.allowOffline === true) {
      toast.warning(
        `⚠️ ${operationType.replace('_', ' ')} allowed offline with override - use caution!`,
        { duration: 5000 }
      );
      return;
    }

    // STRICT BLOCKING: Prevent risky operations offline
    if (!this.isOperationSafeOffline(operationType)) {
      toast.error(
        'LAN-Connection is Down, Please wait for connection to perform this operation.',
        { duration: 8000 }
      );
      throw new Error(`PRODUCTION_SAFETY: ${operationType} blocked offline to prevent data conflicts`);
    }
  }

  /**
   * Create voucher with offline support
   * PRODUCTION-SAFE: Safe operation - allowed offline
   */
  async createVoucher(voucherData: any, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation } = this.getStore();

    // PRODUCTION-SAFE: Check safety (CREATE_VOUCHER is safe, so no blocking)
    this.enforceOfflineSafety('CREATE_VOUCHER', options);
    
    if (isOnline) {
      try {
        const response = await fetch('/api/vouchers', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(voucherData)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success('Voucher created successfully');
        return result;
        
      } catch (error) {
        console.error('Failed to create voucher online:', error);
        
        // If online but API failed, queue for retry
        addOfflineOperation({
          type: 'CREATE_VOUCHER',
          data: voucherData,
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Failed to create voucher - queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'CREATE_VOUCHER',
        data: voucherData,
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info('Voucher saved offline - will sync when connection restored');
      }

      // Return a temporary result for UI feedback
      return {
        id: `offline_${Date.now()}`,
        ...voucherData,
        status: 'PENDING_SYNC',
        isOffline: true
      };
    }
  }

  /**
   * Update voucher with offline support
   * PRODUCTION-SAFE: Safe operation - allowed offline
   */
  async updateVoucher(voucherId: string, updateData: any, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation } = this.getStore();

    // PRODUCTION-SAFE: Check safety (UPDATE_VOUCHER is safe, so no blocking)
    this.enforceOfflineSafety('UPDATE_VOUCHER', options);
    
    if (isOnline) {
      try {
        const response = await fetch(`/api/vouchers/${voucherId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(updateData)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success('Voucher updated successfully');
        return result;
        
      } catch (error) {
        console.error('Failed to update voucher online:', error);
        
        // Queue for retry
        addOfflineOperation({
          type: 'UPDATE_VOUCHER',
          data: { id: voucherId, ...updateData },
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Failed to update voucher - queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'UPDATE_VOUCHER',
        data: { id: voucherId, ...updateData },
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info('Voucher update saved offline - will sync when connection restored');
      }

      return { success: true, isOffline: true };
    }
  }

  /**
   * Send vouchers to audit with offline support
   * PRODUCTION-SAFE: RISKY OPERATION - BLOCKED OFFLINE
   */
  async sendToAudit(voucherIds: string[], dispatchedBy: string, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation, currentUser } = this.getStore();

    // PRODUCTION-SAFE: STRICT BLOCKING - Prevent risky operation offline
    this.enforceOfflineSafety('SEND_TO_AUDIT', options);

    const batchData = {
      department: currentUser?.department || 'FINANCE', // ORIGINAL: Use user's department
      voucherIds,
      dispatchedBy,
      fromAudit: false
    };
    
    if (isOnline) {
      try {
        const response = await fetch('/api/batches', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(batchData)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success(`Batch sent to audit: ${voucherIds.length} vouchers`);
        return result;
        
      } catch (error) {
        console.error('Failed to send batch to audit:', error);
        
        // Queue for retry
        addOfflineOperation({
          type: 'SEND_TO_AUDIT',
          data: batchData,
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Failed to send to audit - queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'SEND_TO_AUDIT',
        data: batchData,
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info(`Batch queued offline: ${voucherIds.length} vouchers - will send when connection restored`);
      }

      return { success: true, isOffline: true, batchId: `offline_batch_${Date.now()}` };
    }
  }

  /**
   * Receive batch with offline support
   * PRODUCTION-SAFE: RISKY OPERATION - BLOCKED OFFLINE
   */
  async receiveBatch(batchId: string, receivedVoucherIds: string[], rejectedVoucherIds: string[], rejectionComments: any, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation } = this.getStore();

    // PRODUCTION-SAFE: STRICT BLOCKING - Prevent risky operation offline
    this.enforceOfflineSafety('RECEIVE_BATCH', options);
    
    const receiveData = {
      batchId,
      receivedVoucherIds,
      rejectedVoucherIds,
      rejectionComments
    };
    
    if (isOnline) {
      try {
        const response = await fetch(`/api/batches/${batchId}/receive`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            receivedVoucherIds,
            rejectedVoucherIds,
            rejectionComments
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success(`Batch processed: ${receivedVoucherIds.length} accepted, ${rejectedVoucherIds.length} rejected`);
        return result;
        
      } catch (error) {
        console.error('Failed to receive batch:', error);
        
        // Queue for retry
        addOfflineOperation({
          type: 'RECEIVE_BATCH',
          data: receiveData,
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Failed to process batch - queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'RECEIVE_BATCH',
        data: receiveData,
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info('Batch processing saved offline - will sync when connection restored');
      }

      return { success: true, isOffline: true };
    }
  }

  /**
   * Update voucher status with offline support
   * PRODUCTION-SAFE: RISKY OPERATION - BLOCKED OFFLINE
   */
  async updateVoucherStatus(voucherId: string, status: string, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation } = this.getStore();

    // PRODUCTION-SAFE: STRICT BLOCKING - Status changes affect workflows
    this.enforceOfflineSafety('UPDATE_STATUS', options);
    
    if (isOnline) {
      try {
        const response = await fetch(`/api/vouchers/${voucherId}/status`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ status })
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success('Voucher status updated');
        return result;
        
      } catch (error) {
        console.error('Failed to update voucher status:', error);
        
        // Queue for retry
        addOfflineOperation({
          type: 'UPDATE_STATUS',
          data: { id: voucherId, status },
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Status update queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'UPDATE_STATUS',
        data: { id: voucherId, status },
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info('Status update saved offline - will sync when connection restored');
      }

      return { success: true, isOffline: true };
    }
  }
}

// Export singleton instance
export const offlineAPI = new OfflineAPI();

/**
 * Frontend Voucher Workflow State Machine
 * Single Source of Truth for voucher tab filtering and state management
 */

export enum WorkflowState {
  // Finance States
  FINANCE_PENDING = 'FINANCE_PENDING',
  FINANCE_PROCESSING = 'FINANCE_PROCESSING',
  FINANCE_CERTIFIED = 'FINANCE_CERTIFIED',
  FINANCE_REJECTED = 'FINANCE_REJECTED',
  FINANCE_RETURNED = 'FINANCE_RETURNED',
  FINANCE_RESUBMISSION_RECEIVED = 'FINANCE_RESUBMISSION_RECEIVED',

  // Audit States
  AUDIT_NEW = 'AUDIT_NEW',
  AUDIT_NEW_RESUBMITTED = 'AUDIT_NEW_RESUBMITTED',
  AUDIT_PENDING_DISPATCH = 'AUDIT_PENDING_DISPATCH',
  AUDIT_PENDING_DISPATCH_REJECTED = 'AUDIT_PENDING_DISPATCH_REJECTED',
  AUDIT_PENDING_DISPATCH_RETURNED = 'AUDIT_PENDING_DISPATCH_RETURNED',
  AUDIT_DISPATCHED = 'AUDIT_DISPATCHED',
  AUDIT_REJECTED_COPY = 'AUDIT_REJECTED_COPY',
  AUDIT_RETURNED_COPY = 'AUDIT_RETURNED_COPY'
}

export enum BadgeType {
  NONE = 'NONE',
  RE_SUBMITTED = 'RE_SUBMITTED',
  RESUBMISSION = 'RESUBMISSION',
  RETURNED = 'RETURNED',
  REJECTED = 'REJECTED'
}

export interface Voucher {
  id: string;
  voucher_id: string;
  workflow_state: WorkflowState;
  badge_type: BadgeType;
  department: string;
  original_department: string;
  is_copy: boolean;
  parent_voucher_id?: string;
  version: number;
  finance_received?: number; // Track if Finance has received the voucher

  // RESUBMISSION OVERRIDE SYSTEM: New visibility flags
  resubmission_certified_visible_to_finance?: number; // Flag for Finance CERTIFIED tab visibility
  resubmission_tracking_visible_to_audit?: number; // Flag for Audit DISPATCHED tab visibility

  [key: string]: any;
}

export interface TabCounts {
  [tabName: string]: number;
}

export class VoucherWorkflowStateMachine {
  private static readonly STATE_TO_TAB_MAPPING = {
    // Finance Dashboard Tabs
    [WorkflowState.FINANCE_PENDING]: 'pending',
    [WorkflowState.FINANCE_PROCESSING]: 'processing',
    [WorkflowState.FINANCE_CERTIFIED]: 'certified',
    [WorkflowState.FINANCE_REJECTED]: 'rejected',
    [WorkflowState.FINANCE_RETURNED]: 'returned',
    [WorkflowState.FINANCE_RESUBMISSION_RECEIVED]: 'processing',

    // Audit Dashboard Tabs
    [WorkflowState.AUDIT_NEW]: 'new-vouchers',
    [WorkflowState.AUDIT_NEW_RESUBMITTED]: 'new-vouchers',
    [WorkflowState.AUDIT_PENDING_DISPATCH]: 'pending-dispatch',
    [WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED]: 'pending-dispatch',
    [WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED]: 'pending-dispatch',
    [WorkflowState.AUDIT_DISPATCHED]: 'dispatched',
    [WorkflowState.AUDIT_REJECTED_COPY]: 'rejected',
    [WorkflowState.AUDIT_RETURNED_COPY]: 'returned'
  };

  private static readonly TAB_DISPLAY_NAMES = {
    // Finance Tabs
    'pending': 'Pending',
    'processing': 'Processing',
    'certified': 'Certified',
    'rejected': 'Rejected',
    'returned': 'Returned',

    // Audit Tabs
    'new-vouchers': 'New Vouchers',
    'pending-dispatch': 'Pending Dispatch',
    'dispatched': 'Dispatched'
  };

  private static readonly BADGE_DISPLAY_NAMES = {
    [BadgeType.NONE]: '',
    [BadgeType.RE_SUBMITTED]: 'RE-SUBMITTED',
    [BadgeType.RETURNED]: 'RETURNED',
    [BadgeType.REJECTED]: 'REJECTED'
  };

  private static readonly BADGE_COLORS = {
    [BadgeType.NONE]: '',
    [BadgeType.RE_SUBMITTED]: 'bg-blue-100 text-blue-800',
    [BadgeType.RETURNED]: 'bg-yellow-100 text-yellow-800',
    [BadgeType.REJECTED]: 'bg-red-100 text-red-800'
  };

  /**
   * Get tab name for voucher based on workflow state and user department
   * ENHANCED: Now supports resubmission override logic for certified resubmissions and returned voucher resubmissions
   */
  static getTabForVoucher(voucher: Voucher, userDepartment: string): string | null {
    const { workflow_state, department, original_department, status, workStarted, dispatched, finance_received,
            is_resubmitted, resubmission_certified_visible_to_finance, is_returned_voucher, return_certified_visible_to_finance,
            is_on_hold } = voucher;

    // RESUBMISSION OVERRIDE SYSTEM: Check resubmission visibility flags first
    // CRITICAL: This preserves original rejection reasons in batch windows and tabs
    if (is_resubmitted === 1 &&
        status === 'VOUCHER CERTIFIED' &&
        resubmission_certified_visible_to_finance === 1 &&
        userDepartment === original_department) {
      return 'certified'; // Shows in Finance CERTIFIED tab with original rejection reason
    }

    // RETURN VOUCHER OVERRIDE SYSTEM: Check return voucher visibility flags
    // Only show in RETURNED tab if NOT certified (Phase 1 only)
    if (is_returned_voucher === 1 &&
        status === 'VOUCHER RETURNED' &&
        return_certified_visible_to_finance === 1 &&
        userDepartment === original_department &&
        !voucher.is_returned_copy) { // Only original vouchers, not copies
      return 'returned';
    }

    // PHASE 2 FIX: Certified returned vouchers (completed resubmission cycle)
    // These should appear in CERTIFIED and DISPATCHED tabs, NOT in RETURNED tab
    if (voucher.is_returned_voucher === 1 &&
        status === 'VOUCHER CERTIFIED' &&
        voucher.return_certified_visible_to_finance === 1) {

      // DUAL-TAB VISIBILITY: For Finance Voucher Hub DISPATCHED tab (viewed by Audit users)
      if (userDepartment === 'AUDIT' && original_department === 'FINANCE') {
        return 'dispatched'; // Shows in Finance Voucher Hub DISPATCHED tab
      }

      // DUAL-TAB VISIBILITY: For Finance Department CERTIFIED tab (viewed by Finance users)
      if (userDepartment === 'FINANCE' && original_department === 'FINANCE') {
        return 'certified'; // Shows in Finance Department CERTIFIED tab
      }
    }

    // HOLD FEATURE OVERRIDE: Keep held vouchers in NEW VOUCHER tab regardless of workflow state
    // This ensures held vouchers never disappear from the NEW VOUCHER tab
    if (is_on_hold === true && userDepartment === 'AUDIT') {
      // Only override for audit vouchers that would normally be in NEW VOUCHER or PENDING DISPATCH
      if (workflow_state === 'AUDIT_NEW' || workflow_state === 'AUDIT_PENDING_DISPATCH') {
        return 'new-vouchers';
      }
    }

    // All vouchers should have workflow_state - no fallback needed

    // New workflow state machine logic
    if (userDepartment === 'AUDIT') {
      if (workflow_state.startsWith('AUDIT_')) {
        return this.STATE_TO_TAB_MAPPING[workflow_state] || null;
      }
      return null;
    }

    // Finance users see finance-related vouchers for their department
    if (workflow_state.startsWith('FINANCE_') && original_department === userDepartment) {
      // CRITICAL FIX: Don't use mapping for certified returned vouchers - let dual visibility handle them
      if (workflow_state === 'FINANCE_RETURNED' &&
          status === 'VOUCHER CERTIFIED' &&
          is_returned_voucher === 1 &&
          return_certified_visible_to_finance === 1) {
        return 'certified'; // Will be handled by dual visibility in getVouchersForTab
      }

      return this.STATE_TO_TAB_MAPPING[workflow_state] || null;
    }



    return null;
  }



  /**
   * Filter vouchers for specific tab and department
   */
  static getVouchersForTab(
    vouchers: Voucher[],
    tabName: string,
    userDepartment: string
  ): Voucher[] {
    const standardVouchers = vouchers.filter(voucher => {
      const voucherTab = this.getTabForVoucher(voucher, userDepartment);
      return voucherTab === tabName;
    });

    // CLEAN DUAL VISIBILITY: Add returned vouchers to both tabs without disrupting workflows
    const dualVisibilityVouchers = this.getDualVisibilityVouchers(vouchers, tabName, userDepartment);

    // Combine and deduplicate
    const allVouchers = [...standardVouchers, ...dualVisibilityVouchers];
    return this.deduplicateVouchers(allVouchers);
  }

  /**
   * CLEAN DUAL VISIBILITY SYSTEM
   * Post-processing approach that doesn't disrupt existing workflows
   */
  private static getDualVisibilityVouchers(
    vouchers: Voucher[],
    tabName: string,
    userDepartment: string
  ): Voucher[] {
    return vouchers.filter(voucher => {
      // Only returned vouchers that have been dispatched
      if (voucher.is_returned_voucher !== 1 || !voucher.dispatched) {
        return false;
      }

      // Must meet dual visibility criteria
      const isDualCandidate = (
        voucher.original_department === 'FINANCE' &&
        voucher.return_certified_visible_to_finance === 1
      );

      if (!isDualCandidate) {
        return false;
      }

      // Show in Finance CERTIFIED tab
      if (userDepartment === 'FINANCE' && tabName === 'certified') {
        return true;
      }

      // Show in Audit DISPATCHED tab
      if (userDepartment === 'AUDIT' && tabName === 'dispatched') {
        return true;
      }

      return false;
    });
  }

  /**
   * Remove duplicate vouchers by ID
   */
  private static deduplicateVouchers(vouchers: Voucher[]): Voucher[] {
    const seen = new Set();
    return vouchers.filter(voucher => {
      if (seen.has(voucher.id)) {
        return false;
      }
      seen.add(voucher.id);
      return true;
    });
  }

  /**
   * Get tab counts for dashboard
   */
  static getTabCounts(vouchers: Voucher[], userDepartment: string): TabCounts {
    const counts: TabCounts = {};

    // Initialize counts based on user department
    if (userDepartment === 'AUDIT') {
      counts['new-vouchers'] = 0;
      counts['pending-dispatch'] = 0;
      counts['dispatched'] = 0;
      counts['rejected'] = 0;
      counts['returned'] = 0;
    } else {
      counts['pending'] = 0;
      counts['processing'] = 0;
      counts['certified'] = 0;
      counts['rejected'] = 0;
      counts['returned'] = 0;
    }

    // Count vouchers for each tab
    vouchers.forEach(voucher => {
      const tab = this.getTabForVoucher(voucher, userDepartment);
      if (tab && counts.hasOwnProperty(tab)) {
        counts[tab]++;
      }

      // DUAL VISIBILITY FIX: Count certified resubmissions in both dispatched and certified tabs
      if (userDepartment === voucher.original_department &&
          voucher.workflow_state === 'AUDIT_DISPATCHED' &&
          voucher.status === 'VOUCHER CERTIFIED' &&
          voucher.is_resubmitted === 1) {
        // Add to both dispatched and certified counts (but avoid double counting)
        if (tab !== 'dispatched' && counts.hasOwnProperty('dispatched')) {
          counts['dispatched']++;
        }
        if (tab !== 'certified' && counts.hasOwnProperty('certified')) {
          counts['certified']++;
        }
      }
    });

    return counts;
  }

  /**
   * Get display name for tab
   */
  static getTabDisplayName(tabName: string): string {
    return this.TAB_DISPLAY_NAMES[tabName] || tabName;
  }

  /**
   * Get badge display name
   */
  static getBadgeDisplayName(badgeType: BadgeType): string {
    return this.BADGE_DISPLAY_NAMES[badgeType] || '';
  }

  /**
   * Get badge CSS classes
   */
  static getBadgeClasses(badgeType: BadgeType): string {
    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
    const colorClasses = this.BADGE_COLORS[badgeType] || '';
    return `${baseClasses} ${colorClasses}`.trim();
  }

  /**
   * Check if voucher should show badge
   */
  static shouldShowBadge(voucher: Voucher): boolean {
    return voucher.badge_type !== BadgeType.NONE;
  }

  /**
   * Get all available tabs for department
   */
  static getAvailableTabs(userDepartment: string): string[] {
    if (userDepartment === 'AUDIT') {
      return ['new-vouchers', 'pending-dispatch', 'dispatched', 'rejected', 'returned'];
    } else {
      return ['pending', 'processing', 'certified', 'rejected', 'returned'];
    }
  }

  /**
   * Check if voucher is editable based on workflow state
   */
  static isVoucherEditable(voucher: Voucher, userDepartment: string): boolean {
    const { workflow_state } = voucher;

    if (userDepartment === 'AUDIT') {
      return workflow_state === WorkflowState.AUDIT_NEW || 
             workflow_state === WorkflowState.AUDIT_NEW_RESUBMITTED ||
             workflow_state === WorkflowState.AUDIT_PENDING_DISPATCH;
    }

    // Finance users can edit pending vouchers
    return workflow_state === WorkflowState.FINANCE_PENDING;
  }

  /**
   * Check if voucher can be dispatched
   */
  static canDispatchVoucher(voucher: Voucher, userDepartment: string): boolean {
    if (userDepartment !== 'AUDIT') return false;

    return voucher.workflow_state === WorkflowState.AUDIT_PENDING_DISPATCH ||
           voucher.workflow_state === WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED ||
           voucher.workflow_state === WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED;
  }

  /**
   * Check if voucher can be resubmitted
   */
  static canResubmitVoucher(voucher: Voucher, userDepartment: string): boolean {
    if (userDepartment === 'AUDIT') return false;

    return voucher.workflow_state === WorkflowState.FINANCE_REJECTED ||
           voucher.workflow_state === WorkflowState.FINANCE_RETURNED;
  }

  /**
   * Get next possible actions for voucher
   */
  static getAvailableActions(voucher: Voucher, userDepartment: string): string[] {
    const actions: string[] = [];

    if (this.isVoucherEditable(voucher, userDepartment)) {
      actions.push('edit');
    }

    if (this.canDispatchVoucher(voucher, userDepartment)) {
      actions.push('dispatch');
    }

    if (this.canResubmitVoucher(voucher, userDepartment)) {
      actions.push('resubmit');
    }

    // Always allow viewing
    actions.push('view');

    return actions;
  }

  /**
   * Sort vouchers by priority (newest first, with special handling for badges)
   */
  static sortVouchers(vouchers: Voucher[]): Voucher[] {
    return vouchers.sort((a, b) => {
      // Priority order: RE_SUBMITTED > RETURNED > REJECTED > NONE
      const badgePriority = {
        [BadgeType.RE_SUBMITTED]: 4,
        [BadgeType.RETURNED]: 3,
        [BadgeType.REJECTED]: 2,
        [BadgeType.NONE]: 1
      };

      const aPriority = badgePriority[a.badge_type] || 1;
      const bPriority = badgePriority[b.badge_type] || 1;

      if (aPriority !== bPriority) {
        return bPriority - aPriority; // Higher priority first
      }

      // If same priority, sort by creation date (newest first)
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });
  }
}

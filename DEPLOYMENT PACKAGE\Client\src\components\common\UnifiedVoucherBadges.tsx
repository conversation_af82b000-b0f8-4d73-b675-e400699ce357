/**
 * Unified Voucher Badges Component
 * Single component for displaying all voucher badges consistently
 * Uses centralized badge logic to eliminate duplicates and ensure persistence
 */

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { getBadgesForTab, getAllVoucherBadges, BadgeInfo } from '@/utils/voucherBadgeUtils';

interface UnifiedVoucherBadgesProps {
  voucher: any;
  tabName?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showWorkflowState?: boolean;
}

export const UnifiedVoucherBadges: React.FC<UnifiedVoucherBadgesProps> = ({
  voucher,
  tabName,
  className = '',
  size = 'sm',
  showWorkflowState = false
}) => {
  // Get badges based on context (tab-specific or all badges)
  const badges = tabName ? getBadgesForTab(voucher, tabName) : getAllVoucherBadges(voucher);

  // Add workflow state badge for debugging if requested
  if (showWorkflowState && voucher.workflow_state) {
    badges.push({
      shouldShow: true,
      text: voucher.workflow_state,
      className: 'bg-purple-100 text-purple-800 font-mono',
      type: 'status'
    } as BadgeInfo);
  }

  if (badges.length === 0) {
    return null;
  }

  // Size classes for consistent styling
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm'
  };

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {badges.map((badge, index) => {
        // REQUIREMENT 2: Special styling for CERTIFIED-RESUBMISSION badges
        const isSpecialBadge = badge.type === 'certified-resubmission';
        const badgeClassName = isSpecialBadge
          ? `${badge.className} ${sizeClasses[size]} font-bold tracking-wide animate-pulse`
          : `${badge.className} ${sizeClasses[size]} font-medium`;

        return (
          <Badge
            key={`${badge.type}-${index}`}
            className={badgeClassName}
          >
            {badge.text}
          </Badge>
        );
      })}
    </div>
  );
};

// Simplified component for just resubmission badges
interface ResubmissionBadgeProps {
  voucher: any;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const ResubmissionBadge: React.FC<ResubmissionBadgeProps> = ({
  voucher,
  className = '',
  size = 'sm'
}) => {
  const badges = getAllVoucherBadges(voucher).filter(badge => 
    badge.type === 'resubmission' || badge.type === 'certified-resubmission'
  );

  if (badges.length === 0) {
    return null;
  }

  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm'
  };

  return (
    <>
      {badges.map((badge, index) => (
        <Badge
          key={`resubmission-${index}`}
          className={`${badge.className} ${sizeClasses[size]} font-medium ${className}`}
        >
          {badge.text}
        </Badge>
      ))}
    </>
  );
};

// Simplified component for just return badges
interface ReturnBadgeProps {
  voucher: any;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const ReturnBadge: React.FC<ReturnBadgeProps> = ({
  voucher,
  className = '',
  size = 'sm'
}) => {
  const badges = getAllVoucherBadges(voucher).filter(badge =>
    badge.type === 'return' || badge.type === 'certified-return' || badge.type === 'resubmitted-return'
  );

  if (badges.length === 0) {
    return null;
  }

  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm'
  };

  return (
    <>
      {badges.map((badge, index) => (
        <Badge
          key={`return-${index}`}
          className={`${badge.className} ${sizeClasses[size]} font-medium ${className}`}
        >
          {badge.text}
        </Badge>
      ))}
    </>
  );
};

export default UnifiedVoucherBadges;

"use strict";
/**
 * Network Integration Service
 * Orchestrates all network services for bulletproof deployment
 * Provides unified interface for hybrid network management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.networkIntegrationService = exports.NetworkIntegrationService = void 0;
const events_1 = require("events");
const hybrid_network_service_js_1 = require("./hybrid-network-service.js");
const intelligent_fallback_service_js_1 = require("./intelligent-fallback-service.js");
const self_healing_network_monitor_js_1 = require("./self-healing-network-monitor.js");
const network_discovery_service_js_1 = require("./network-discovery-service.js");
const portable_deployment_service_js_1 = require("./portable-deployment-service.js");
// import { vmsAdminService } from './vms-admin-service.js';
const logger_js_1 = require("../utils/logger.js");
class NetworkIntegrationService extends events_1.EventEmitter {
    _isInitialized = false;
    _isRunning = false;
    _startupTime = null;
    constructor() {
        super();
        logger_js_1.logger.info('🌐 Network Integration Service initialized');
    }
    /**
     * Initialize and start all network services
     */
    async initialize() {
        try {
            if (this._isInitialized) {
                logger_js_1.logger.warn('Network Integration Service already initialized');
                return;
            }
            logger_js_1.logger.info('🚀 Initializing Network Integration System...');
            const startTime = Date.now();
            // Phase 1: Initialize portable deployment (must be first)
            logger_js_1.logger.info('📁 Phase 1: Initializing portable deployment...');
            await portable_deployment_service_js_1.portableDeploymentService.initialize();
            // Phase 2: Start hybrid network management
            logger_js_1.logger.info('🌐 Phase 2: Starting hybrid network management...');
            await hybrid_network_service_js_1.hybridNetworkService.start();
            // Phase 3: Start intelligent fallback system
            logger_js_1.logger.info('🧠 Phase 3: Starting intelligent fallback system...');
            await intelligent_fallback_service_js_1.intelligentFallbackService.start();
            // Phase 4: Start self-healing monitor
            logger_js_1.logger.info('🏥 Phase 4: Starting self-healing monitor...');
            await self_healing_network_monitor_js_1.selfHealingNetworkMonitor.start();
            // Phase 5: Start VMS-ADMIN service
            logger_js_1.logger.info('🎛️ Phase 5: Starting VMS-ADMIN service...');
            await vms_admin_service_js_1.vmsAdminService.start();
            // Phase 6: Setup event listeners and integration
            this.setupEventListeners();
            const initTime = Date.now() - startTime;
            this._isInitialized = true;
            this._isRunning = true;
            this._startupTime = new Date();
            logger_js_1.logger.info('✅ Network Integration System initialized successfully');
            logger_js_1.logger.info(`⏱️ Initialization completed in ${initTime}ms`);
            // Log system status
            const status = await this.getSystemStatus();
            logger_js_1.logger.info(`🎯 System Status: ${status.overall}`);
            logger_js_1.logger.info(`🔗 Server URL: ${status.serverURL}`);
            logger_js_1.logger.info(`⚙️ Network Mode: ${status.currentMode}`);
            // Emit initialization complete event
            this.emit('initialized', status);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to initialize Network Integration System:', error);
            throw error;
        }
    }
    /**
     * Gracefully shutdown all network services
     */
    async shutdown() {
        try {
            if (!this._isRunning) {
                return;
            }
            logger_js_1.logger.info('🛑 Shutting down Network Integration System...');
            // Stop services in reverse order
            // await vmsAdminService.stop();
            await self_healing_network_monitor_js_1.selfHealingNetworkMonitor.stop();
            await intelligent_fallback_service_js_1.intelligentFallbackService.stop();
            await hybrid_network_service_js_1.hybridNetworkService.stop();
            this._isRunning = false;
            logger_js_1.logger.info('✅ Network Integration System shutdown complete');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error during Network Integration System shutdown:', error);
        }
    }
    /**
     * Setup event listeners for service integration
     */
    setupEventListeners() {
        // Listen to hybrid network events
        hybrid_network_service_js_1.hybridNetworkService.on('networkChange', (event) => {
            logger_js_1.logger.info(`🔄 Network change detected: ${event.type}`);
            this.emit('networkChange', event);
        });
        // Listen to fallback decisions
        intelligent_fallback_service_js_1.intelligentFallbackService.on('fallbackDecision', (decision) => {
            logger_js_1.logger.info(`🎯 Fallback decision: ${decision.action} (${decision.confidence}% confidence)`);
            this.emit('fallbackDecision', decision);
        });
        // Listen to healing actions
        self_healing_network_monitor_js_1.selfHealingNetworkMonitor.on('healingAction', (action) => {
            logger_js_1.logger.info(`🏥 Healing action: ${action.description} - ${action.success ? 'Success' : 'Failed'}`);
            this.emit('healingAction', action);
        });
        // Listen to health checks
        self_healing_network_monitor_js_1.selfHealingNetworkMonitor.on('healthCheck', (result) => {
            logger_js_1.logger.debug(`💓 Health check completed in ${result.duration}ms`);
            this.emit('healthCheck', result);
        });
        logger_js_1.logger.info('📡 Event listeners configured');
    }
    /**
     * Get comprehensive system status
     */
    async getSystemStatus() {
        try {
            const hybridConfig = hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration();
            const issues = [];
            const recommendations = [];
            // Check component status
            const components = {
                hybridNetwork: hybrid_network_service_js_1.hybridNetworkService.isRunning(),
                intelligentFallback: intelligent_fallback_service_js_1.intelligentFallbackService.isRunning(),
                selfHealing: self_healing_network_monitor_js_1.selfHealingNetworkMonitor.isRunning(),
                networkDiscovery: network_discovery_service_js_1.networkDiscoveryService.isServiceRunning(),
                portableDeployment: portable_deployment_service_js_1.portableDeploymentService.isInitialized(),
                vmsAdmin: false // vmsAdminService.isRunning()
            };
            // Determine overall health
            let overall = 'healthy';
            if (!components.hybridNetwork) {
                issues.push('Hybrid network service not running');
                overall = 'critical';
            }
            if (!components.networkDiscovery) {
                issues.push('Network discovery service not active');
                if (overall !== 'critical')
                    overall = 'degraded';
            }
            if (!components.intelligentFallback) {
                issues.push('Intelligent fallback system not active');
                recommendations.push('Restart fallback system for optimal performance');
            }
            if (!components.selfHealing) {
                issues.push('Self-healing monitor not active');
                recommendations.push('Enable self-healing for proactive network management');
            }
            // Check network connectivity
            if (!hybridConfig.isStaticAvailable && !hybridConfig.isDynamicAvailable) {
                issues.push('No network connectivity available');
                overall = 'offline';
            }
            else if (!hybridConfig.isStaticAvailable) {
                issues.push('Static IP not available');
                recommendations.push('Check static IP configuration');
                if (overall === 'healthy')
                    overall = 'degraded';
            }
            // Add recommendations based on current mode
            if (hybridConfig.mode === 'dynamic' && hybridConfig.isStaticAvailable) {
                recommendations.push('Consider switching to static mode for better performance');
            }
            return {
                overall,
                components,
                currentMode: hybridConfig.mode,
                serverURL: hybrid_network_service_js_1.hybridNetworkService.getServerURL(),
                lastUpdate: new Date(),
                issues,
                recommendations
            };
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error getting system status:', error);
            return {
                overall: 'critical',
                components: {
                    hybridNetwork: false,
                    intelligentFallback: false,
                    selfHealing: false,
                    networkDiscovery: false,
                    portableDeployment: false,
                    vmsAdmin: false
                },
                currentMode: 'dynamic',
                serverURL: 'http://localhost:8080',
                lastUpdate: new Date(),
                issues: ['System status check failed'],
                recommendations: ['Restart network services']
            };
        }
    }
    /**
     * Get comprehensive system statistics
     */
    async getSystemStats() {
        try {
            return {
                uptime: this._startupTime ? Date.now() - this._startupTime.getTime() : 0,
                initialized: this._isInitialized,
                running: this._isRunning,
                networkConfig: hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration(),
                fallbackStats: intelligent_fallback_service_js_1.intelligentFallbackService.getStats(),
                healingStats: self_healing_network_monitor_js_1.selfHealingNetworkMonitor.getStats(),
                discoveryStats: network_discovery_service_js_1.networkDiscoveryService.getNetworkStats(),
                deploymentConfig: portable_deployment_service_js_1.portableDeploymentService.getDeploymentConfig()
            };
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error getting system stats:', error);
            return null;
        }
    }
    /**
     * Force network mode switch
     */
    async switchNetworkMode(mode) {
        try {
            logger_js_1.logger.info(`🔄 Manually switching to ${mode} mode...`);
            let success = false;
            if (mode === 'static') {
                success = await hybrid_network_service_js_1.hybridNetworkService.switchToStaticMode();
            }
            else {
                success = await hybrid_network_service_js_1.hybridNetworkService.switchToDynamicModeManual();
            }
            if (success) {
                logger_js_1.logger.info(`✅ Successfully switched to ${mode} mode`);
                this.emit('modeSwitch', { mode, success: true });
            }
            else {
                logger_js_1.logger.warn(`⚠️ Failed to switch to ${mode} mode`);
                this.emit('modeSwitch', { mode, success: false });
            }
            return success;
        }
        catch (error) {
            logger_js_1.logger.error(`❌ Error switching to ${mode} mode:`, error);
            return false;
        }
    }
    /**
     * Trigger manual healing
     */
    async triggerHealing() {
        try {
            logger_js_1.logger.info('🏥 Triggering manual healing...');
            // Force a health check which will trigger healing if needed
            await self_healing_network_monitor_js_1.selfHealingNetworkMonitor.performHealthCheck();
            return true;
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error triggering manual healing:', error);
            return false;
        }
    }
    /**
     * Restart all network services
     */
    async restartServices() {
        try {
            logger_js_1.logger.info('🔄 Restarting all network services...');
            await this.shutdown();
            await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
            await this.initialize();
            logger_js_1.logger.info('✅ All network services restarted successfully');
            return true;
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error restarting network services:', error);
            return false;
        }
    }
    /**
     * Get current server URL
     */
    getServerURL() {
        return hybrid_network_service_js_1.hybridNetworkService.getServerURL();
    }
    /**
     * Check if system is ready
     */
    isReady() {
        return this._isInitialized && this._isRunning;
    }
    /**
     * Get deployment information
     */
    getDeploymentInfo() {
        const deploymentConfig = portable_deployment_service_js_1.portableDeploymentService.getDeploymentConfig();
        const networkConfig = hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration();
        return {
            deploymentId: deploymentConfig?.deploymentId,
            deploymentTime: deploymentConfig?.deploymentTime,
            deploymentMode: deploymentConfig?.deploymentMode,
            vmsPath: deploymentConfig?.paths.vmsRoot,
            serverIP: networkConfig.currentIP,
            serverPort: networkConfig.port,
            networkMode: networkConfig.mode,
            staticIPAvailable: networkConfig.isStaticAvailable,
            dynamicIPAvailable: networkConfig.isDynamicAvailable
        };
    }
    /**
     * Export system configuration for backup/restore
     */
    async exportConfiguration() {
        try {
            const status = await this.getSystemStatus();
            const stats = await this.getSystemStats();
            const deploymentInfo = this.getDeploymentInfo();
            return {
                exportTime: new Date(),
                version: '1.0.0',
                status,
                stats,
                deploymentInfo,
                configuration: {
                    hybrid: hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration(),
                    fallback: intelligent_fallback_service_js_1.intelligentFallbackService.getStats(),
                    healing: self_healing_network_monitor_js_1.selfHealingNetworkMonitor.getStats()
                }
            };
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error exporting configuration:', error);
            return null;
        }
    }
}
exports.NetworkIntegrationService = NetworkIntegrationService;
// Export singleton instance
exports.networkIntegrationService = new NetworkIntegrationService();
//# sourceMappingURL=network-integration-service.js.map
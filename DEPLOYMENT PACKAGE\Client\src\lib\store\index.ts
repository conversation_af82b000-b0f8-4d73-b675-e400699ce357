
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AppState } from './types';

// Import all slices
import { createAuthSlice, AuthSlice } from './slices/auth-slice';
import { createUsersSlice, UsersSlice } from './slices/users-slice';
import { createVouchersSlice, VouchersSlice } from './slices/vouchers-slice';
import { createVoucherBatchesSlice, VoucherBatchesSlice } from './slices/voucher-batches-slice';
import { createBlacklistedVoucherIdsSlice, BlacklistedVoucherIdsSlice } from './slices/blacklisted-voucher-ids-slice';
import { createProvisionalCashRecordsSlice, ProvisionalCashRecordsSlice } from './slices/provisional-cash-records-slice';
import { createNotificationsSlice, NotificationsSlice } from './slices/notifications-slice';
import { createDepartmentSlice, DepartmentSlice } from './slices/department-slice';
import { createAuditSlice, AuditSlice } from './slices/audit-slice';
import { createAdminSlice, AdminSlice } from './slices/admin-slice';
import { createCleanupSlice, CleanupSlice } from './slices/cleanup-slice';
import { createResourceLocksSlice, ResourceLocksSlice } from './slices/resource-locks-slice';
import { createResourceViewersSlice, ResourceViewersSlice } from './slices/resource-viewers-slice';
import { createOfflineSlice } from './slices/offline-slice';

export const useAppStore = create<AppState>()(
  persist(
    (set, get, api) => ({
      ...createAuthSlice(set, get, api),
      ...createUsersSlice(set, get, api),
      ...createVouchersSlice(set, get, api),
      ...createVoucherBatchesSlice(set, get, api),
      ...createBlacklistedVoucherIdsSlice(set, get, api),
      ...createProvisionalCashRecordsSlice(set, get, api),
      ...createNotificationsSlice(set, get, api),
      ...createDepartmentSlice(set, get, api),
      ...createAuditSlice(set, get, api),
      ...createAdminSlice(set, get, api),
      ...createCleanupSlice(set, get, api),
      ...createResourceLocksSlice(set, get, api),
      ...createResourceViewersSlice(set, get, api),
      ...createOfflineSlice(set, get, api),
    }),
    {
      name: 'voucher-management-system'
    }
  )
);

// Re-export for convenience
export * from './types';

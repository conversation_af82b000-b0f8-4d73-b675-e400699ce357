import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, Lock, AlertTriangle, CheckCircle, Send } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { usersApi } from '@/lib/api';
import { departments } from '@/lib/data';
import { toast } from 'sonner';

interface PasswordChangeRequestDialogProps {
  open: boolean;
  onClose: () => void;
}

export function PasswordChangeRequestDialog({ open, onClose }: PasswordChangeRequestDialogProps) {
  const [selectedUserId, setSelectedUserId] = useState('');
  const [department, setDepartment] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [departmentUsers, setDepartmentUsers] = useState<any[]>([]);

  // Fetch all users when dialog opens
  React.useEffect(() => {
    if (open) {
      fetchAllUsers();
    }
  }, [open]);

  // Filter users by department
  React.useEffect(() => {
    if (department && allUsers.length > 0) {
      const filtered = allUsers.filter(user => user.department === department);
      setDepartmentUsers(filtered);
      setSelectedUserId(''); // Reset user selection when department changes
    } else {
      setDepartmentUsers([]);
      setSelectedUserId('');
    }
  }, [department, allUsers]);

  const fetchAllUsers = async () => {
    try {
      const users = await usersApi.getPublicUsers();
      setAllUsers(users);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError('Failed to load users. Please try again.');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validation
    if (!selectedUserId || !department || !newPassword || !confirmPassword) {
      setError('All fields are required');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('New passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      setError('New password must be at least 6 characters long');
      return;
    }

    // Find selected user details
    const selectedUser = allUsers.find(user => user.id === selectedUserId);
    if (!selectedUser) {
      setError('Selected user not found');
      return;
    }

    setIsLoading(true);

    try {
      // Submit password change request to admin
      await usersApi.requestPasswordChange({
        userId: selectedUserId,
        name: selectedUser.name,
        department: selectedUser.department,
        newPassword
      });

      toast.success('Password change request submitted!', {
        description: 'Your request has been sent to the administrator for approval. You will be notified once it is processed.',
        duration: 8000,
      });

      // Reset form
      setSelectedUserId('');
      setDepartment('');
      setNewPassword('');
      setConfirmPassword('');
      setError('');
      onClose();

    } catch (error: any) {
      console.error('Password change request error:', error);

      if (error.response?.status === 409) {
        setError('A password change request for this user is already pending');
      } else if (error.response?.status === 404) {
        setError('User not found. Please check your name and department.');
      } else {
        setError(error.response?.data?.error || 'Failed to submit password change request. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setSelectedUserId('');
      setDepartment('');
      setNewPassword('');
      setConfirmPassword('');
      setError('');
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Request Password Change
          </DialogTitle>
          <DialogDescription>
            Submit a request to change your password. An administrator will review and approve your request.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="department">Department</Label>
            <Select value={department} onValueChange={setDepartment} disabled={isLoading}>
              <SelectTrigger>
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map((dept) => (
                  <SelectItem key={dept} value={dept}>
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="user">User</Label>
            <Select value={selectedUserId} onValueChange={setSelectedUserId} disabled={isLoading || !department}>
              <SelectTrigger>
                <SelectValue placeholder={department ? "Select user" : "Select department first"} />
              </SelectTrigger>
              <SelectContent>
                {departmentUsers.map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {department && departmentUsers.length === 0 && (
              <p className="text-sm text-gray-500">No users found in {department} department</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="newPassword">New Password</Label>
            <div className="relative">
              <Input
                id="newPassword"
                type={showNewPassword ? 'text' : 'password'}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Enter your new password"
                disabled={isLoading}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowNewPassword(!showNewPassword)}
                disabled={isLoading}
              >
                {showNewPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {newPassword && newPassword.length < 6 && (
              <p className="text-sm text-orange-600">Password must be at least 6 characters long</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm New Password</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm your new password"
                disabled={isLoading}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isLoading}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {confirmPassword && newPassword && confirmPassword !== newPassword && (
              <p className="text-sm text-red-600">Passwords do not match</p>
            )}
            {confirmPassword && newPassword && confirmPassword === newPassword && (
              <p className="text-sm text-green-600 flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                Passwords match
              </p>
            )}
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !selectedUserId || !department || !newPassword || !confirmPassword || newPassword !== confirmPassword}
            >
              {isLoading ? 'Submitting...' : 'Submit Request'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

/**
 * Voucher Badge Utilities
 * Centralized logic for determining voucher badge display
 * This is the single source of truth for all badge-related decisions
 */

import { Voucher } from '@/lib/types';

export interface BadgeInfo {
  shouldShow: boolean;
  text: string;
  className: string;
  type: 'resubmission' | 'certified-resubmission' | 'return' | 'certified-return' | 'resubmitted-return' | 'status' | 'copy';
  priority: number; // Higher number = higher priority
}

/**
 * MASTER FUNCTION: Determines if a voucher should show a resubmission badge
 * This is the single source of truth for resubmission badge logic
 * FIXED: Now properly distinguishes between rejection-in-progress vs actual resubmissions
 */
export function shouldShowResubmissionBadge(voucher: any): boolean {
  if (!voucher) return false;

  // PRIMARY INDICATOR: is_resubmitted flag (most reliable)
  const isResubmitted = voucher.is_resubmitted === true ||
                       voucher.is_resubmitted === 1 ||
                       voucher.isResubmitted === true ||
                       voucher.isResubmitted === 1;

  // If explicitly marked as resubmitted, always show badge
  if (isResubmitted) {
    return true;
  }

  // SECONDARY INDICATORS: Only for actual resubmissions, not rejection-in-progress
  const hasResubmissionStatus = voucher.status === 'RE-SUBMISSION';
  const hasResubmissionWorkflow = voucher.workflow_state === 'AUDIT_NEW_RESUBMITTED' ||
                                 voucher.workflowState === 'AUDIT_NEW_RESUBMITTED' ||
                                 voucher.workflow_state?.includes('RESUBMISSION') ||
                                 voucher.workflowState?.includes('RESUBMISSION');

  // CRITICAL FIX: Only show badge for actual resubmissions, not vouchers in rejection workflow
  // Rejection workflow states that should NOT show resubmission badges:
  const isInRejectionWorkflow = voucher.workflow_state === 'AUDIT_PENDING_DISPATCH_REJECTED' ||
                               voucher.workflow_state === 'FINANCE_REJECTED' ||
                               voucher.workflow_state === 'AUDIT_REJECTED_COPY';

  // If voucher is in rejection workflow, don't show resubmission badge
  if (isInRejectionWorkflow) {
    return false;
  }

  // Show badge only for confirmed resubmissions
  return hasResubmissionStatus || hasResubmissionWorkflow;
}

/**
 * Determines the specific type of resubmission badge to show
 * ENHANCED: Now supports distinct CERTIFIED-RESUBMISSION badges with persistence
 */
export function getResubmissionBadgeInfo(voucher: any): BadgeInfo | null {
  if (!shouldShowResubmissionBadge(voucher)) {
    return null;
  }

  // REQUIREMENT 2: Check if this is a certified resubmission
  const isCertified = voucher.certified_by || voucher.certifiedBy ||
                     voucher.status === 'VOUCHER CERTIFIED' ||
                     voucher.workflow_state === 'AUDIT_DISPATCHED' ||
                     voucher.workflow_state === 'FINANCE_CERTIFIED';

  // REQUIREMENT 2: Permanent CERTIFIED-RESUBMISSION badge for certified resubmissions
  if (isCertified) {
    return {
      shouldShow: true,
      text: 'CERTIFIED-RESUBMISSION',
      className: 'bg-purple-700 text-white border border-purple-800 font-semibold shadow-sm',
      type: 'certified-resubmission',
      priority: 10 // Highest priority - always shows
    };
  }

  // Regular resubmission badge
  return {
    shouldShow: true,
    text: 'RESUBMISSION',
    className: 'bg-purple-500 text-white border border-purple-600',
    type: 'resubmission',
    priority: 8 // High priority
  };
}

/**
 * RETURNED VOUCHER TRACKING: Determines if a voucher should show a return badge
 * Mirror resubmission badge logic for returned vouchers
 */
export function shouldShowReturnBadge(voucher: any): boolean {
  if (!voucher) return false;

  // PRIMARY INDICATOR: is_returned_voucher flag (most reliable)
  const isReturnedVoucher = voucher.is_returned_voucher === true ||
                           voucher.is_returned_voucher === 1 ||
                           voucher.isReturnedVoucher === true ||
                           voucher.isReturnedVoucher === 1;

  // If explicitly marked as returned voucher, always show badge
  if (isReturnedVoucher) {
    return true;
  }

  // SECONDARY INDICATORS: Only for actual returns, not return-in-progress
  const hasReturnStatus = voucher.status === 'VOUCHER RETURNED';
  const hasReturnWorkflow = voucher.workflow_state === 'AUDIT_PENDING_DISPATCH_RETURNED' ||
                           voucher.workflow_state === 'AUDIT_RETURNED_COPY' ||
                           voucher.workflow_state === 'FINANCE_RETURNED' ||
                           voucher.workflowState === 'AUDIT_PENDING_DISPATCH_RETURNED' ||
                           voucher.workflowState === 'AUDIT_RETURNED_COPY' ||
                           voucher.workflowState === 'FINANCE_RETURNED';

  // Check for return history indicators
  const hasReturnHistory = voucher.returned_by && voucher.returned_by.trim() !== '' ||
                          voucher.returnedBy && voucher.returnedBy.trim() !== '' ||
                          voucher.return_comment && voucher.return_comment.trim() !== '' ||
                          voucher.returnComment && voucher.returnComment.trim() !== '';

  // Show badge for confirmed returns
  return hasReturnStatus || hasReturnWorkflow || hasReturnHistory;
}

/**
 * Determines the specific type of return badge to show
 * RETURNED VOUCHER TRACKING: Mirror certified resubmission badge logic for returns
 */
export function getReturnBadgeInfo(voucher: any): BadgeInfo | null {
  if (!shouldShowReturnBadge(voucher)) {
    return null;
  }

  // ENHANCED FIX: Check if this is a returned voucher that has been resubmitted
  // This covers both Phase 1 (pending) and Phase 2 (certified) returned resubmissions
  // ROBUST: Check multiple resubmission indicators (not just is_resubmitted flag)
  const isResubmission = (
    voucher.is_resubmitted === 1 ||
    voucher.isResubmitted === true ||
    voucher.resubmission_count > 0 ||
    voucher.last_resubmission_date ||
    (voucher.certified_by || voucher.certifiedBy) ||
    (voucher.certified_amount && voucher.certified_amount > 0) ||
    (voucher.certifiedAmount && voucher.certifiedAmount > 0)
  );

  const isResubmittedReturn = (
    // Must be a returned voucher
    (voucher.is_returned_voucher === 1 || voucher.return_count > 0) &&
    // AND must be resubmitted (using robust detection)
    isResubmission
  );

  // ENHANCED: Check for ANY certified returned voucher (not just resubmitted ones)
  const isCertifiedReturnedVoucher = (
    (voucher.is_returned_voucher === 1 || voucher.return_count > 0) &&
    (
      (voucher.certified_by || voucher.certifiedBy) ||
      (voucher.certified_amount && voucher.certified_amount > 0) ||
      (voucher.certifiedAmount && voucher.certifiedAmount > 0)
    ) &&
    (voucher.return_certified_visible_to_finance === 1 ||
     voucher.badge_persistence_flags?.certified_returned === true ||
     voucher.badge_persistence_flags?.phase_2_complete === true ||
     voucher.badge_persistence_flags?.dual_tab_visibility === true)
  );

  if (isCertifiedReturnedVoucher) {
    return {
      shouldShow: true,
      text: 'CERTIFIED-RETURNED',
      className: 'bg-gradient-to-r from-purple-600 to-orange-600 text-white border border-purple-800 font-semibold shadow-lg animate-pulse',
      type: 'certified-returned',
      priority: 12 // Highest priority for Phase 2
    };
  }

  // FINAL RESTING PLACE BADGE: Check if this is a certified returned voucher in its final tabs
  // This badge appears permanently in CERTIFIED tab and DISPATCHED tab for user reference
  const isInFinalRestingPlace = (
    (voucher.is_returned_voucher === 1 || voucher.return_count > 0) &&
    (voucher.return_certified_visible_to_finance === 1 ||
     voucher.badge_persistence_flags?.certified_returned === true ||
     voucher.badge_persistence_flags?.phase_2_complete === true ||
     voucher.badge_persistence_flags?.dual_tab_visibility === true)
  );

  if (isInFinalRestingPlace) {
    return {
      shouldShow: true,
      text: 'CERTIFIED-RETURNED',
      className: 'bg-gradient-to-r from-purple-600 to-orange-600 text-white border border-purple-800 font-semibold shadow-lg animate-pulse',
      type: 'certified-returned-final',
      priority: 15 // HIGHEST priority for final resting place
    };
  }

  if (isResubmittedReturn) {
    // Phase 1: Regular returned voucher resubmission (pending certification)
    return {
      shouldShow: true,
      text: 'RESUBMITTED-RETURNED VOUCHER',
      className: 'bg-purple-700 text-white border border-purple-800 font-semibold shadow-sm',
      type: 'resubmitted-return',
      priority: 11 // High priority for Phase 1
    };
  }

  // CRITICAL FIX: Only show CERTIFIED-RETURN badge for returned vouchers that were resubmitted and certified
  // A returned voucher should only be "certified" if it went through the full resubmission cycle
  const isActuallyCertifiedReturn = (
    // Must be a returned voucher
    (voucher.is_returned_voucher === 1 || voucher.return_count > 0) &&
    // AND must be in a certified state (completed the resubmission cycle)
    (voucher.status === 'VOUCHER CERTIFIED' ||
     voucher.workflow_state === 'AUDIT_DISPATCHED' ||
     voucher.workflow_state === 'FINANCE_CERTIFIED' ||
     voucher.certified_by || voucher.certifiedBy) &&
    // AND must have return certified visibility flag (indicates completed Phase 2)
    (voucher.return_certified_visible_to_finance === 1 ||
     voucher.badge_persistence_flags?.certified_returned === true)
  );

  // CERTIFIED-RETURN badge only for truly certified returns (orange with animation)
  if (isActuallyCertifiedReturn) {
    return {
      shouldShow: true,
      text: 'CERTIFIED-RETURN',
      className: 'bg-orange-600 text-white border border-orange-700 font-semibold shadow-sm animate-pulse',
      type: 'certified-return',
      priority: 9 // High priority - always shows for certified returns
    };
  }

  // Regular return badge (orange)
  return {
    shouldShow: true,
    text: 'RETURN',
    className: 'bg-orange-500 text-white border border-orange-600',
    type: 'return',
    priority: 7 // High priority
  };
}

/**
 * Gets all badges that should be displayed for a voucher
 * Returns an array of badge information objects
 */
export function getAllVoucherBadges(voucher: any): BadgeInfo[] {
  const badges: BadgeInfo[] = [];

  // 1. Resubmission badge (highest priority)
  const resubmissionBadge = getResubmissionBadgeInfo(voucher);
  if (resubmissionBadge) {
    badges.push(resubmissionBadge);
  }

  // 2. Return badge (high priority, but lower than resubmission)
  const returnBadge = getReturnBadgeInfo(voucher);
  if (returnBadge) {
    // CRITICAL FIX: For resubmitted returned vouchers, the return badge takes precedence
    // because it shows the more specific "RESUBMITTED-RETURNED VOUCHER" text
    if (returnBadge.type === 'resubmitted-return') {
      // Replace resubmission badge with the more specific resubmitted-return badge
      const resubmissionIndex = badges.findIndex(b => b.type === 'resubmission' || b.type === 'certified-resubmission');
      if (resubmissionIndex >= 0) {
        badges[resubmissionIndex] = returnBadge;
      } else {
        badges.push(returnBadge);
      }
    } else if (!resubmissionBadge) {
      // Only show regular return badge if there's no resubmission badge
      badges.push(returnBadge);
    }
  }

  // 3. Copy badge
  if (voucher.is_copy) {
    badges.push({
      shouldShow: true,
      text: 'COPY',
      className: 'bg-gray-100 text-gray-800 border border-gray-300',
      type: 'copy',
      priority: 5 // Medium priority
    });
  }

  // 4. Status badge (only if not already covered by resubmission or return)
  if (!resubmissionBadge && !returnBadge && voucher.status && voucher.status !== 'VOUCHER CERTIFIED') {
    badges.push({
      shouldShow: true,
      text: voucher.status,
      className: getStatusBadgeClassName(voucher.status),
      type: 'status',
      priority: 3 // Lower priority
    });
  }

  // Sort badges by priority (highest first)
  badges.sort((a, b) => (b.priority || 0) - (a.priority || 0));

  return badges;
}

/**
 * Helper function to get status badge styling
 */
function getStatusBadgeClassName(status: string): string {
  switch (status) {
    case 'RE-SUBMISSION':
      return 'bg-purple-500 text-white';
    case 'PENDING SUBMISSION':
      return 'border border-gray-200 text-gray-700';
    case 'VOUCHER RETURNED':
      return 'bg-gray-500 text-white';
    case 'VOUCHER REJECTED':
      return 'bg-red-500 text-white';
    case 'VOUCHER CERTIFIED':
      return 'bg-green-500 text-white';
    default:
      return 'border border-gray-200 text-gray-700';
  }
}

/**
 * Context-aware badge display for specific tabs
 * REQUIREMENT 2: Enhanced with permanent CERTIFIED-RESUBMISSION badge persistence
 */
export function getBadgesForTab(voucher: any, tabName: string): BadgeInfo[] {
  const allBadges = getAllVoucherBadges(voucher);

  // PRODUCTION FIX: All Finance department tabs need persistent badge display
  const financeTabsWithPersistence = [
    'dispatched', 'certified', 'pending', 'processing', 'rejected', 'returned',
    'pending-submission', 'certified-resubmission'
  ];

  // PRODUCTION FIX: All Audit department tabs need persistent badge display
  const auditTabsWithPersistence = [
    'new-vouchers', 'pending-dispatch', 'dispatched', 'rejected', 'returned-vouchers'
  ];

  // REQUIREMENT 2: Persistent badge display for key tabs
  if (financeTabsWithPersistence.includes(tabName) || auditTabsWithPersistence.includes(tabName)) {
    const persistentBadges = allBadges.filter(badge =>
      badge.type === 'resubmission' ||
      badge.type === 'certified-resubmission' ||
      badge.type === 'return' ||
      badge.type === 'certified-return' ||
      badge.type === 'resubmitted-return' ||
      badge.type === 'certified-resubmitted-return' ||
      badge.type === 'copy'
    );

    // REQUIREMENT 2: Ensure badges are always visible in these tabs
    if (persistentBadges.length === 0) {
      // Check for resubmission badge
      if (shouldShowResubmissionBadge(voucher)) {
        const resubmissionBadge = getResubmissionBadgeInfo(voucher);
        if (resubmissionBadge) {
          persistentBadges.push(resubmissionBadge);
        }
      }

      // Check for return badge
      if (shouldShowReturnBadge(voucher)) {
        const returnBadge = getReturnBadgeInfo(voucher);
        if (returnBadge) {
          persistentBadges.push(returnBadge);
        }
      }
    }

    return persistentBadges;
  }

  // For other tabs, show all relevant badges
  return allBadges;
}

/**
 * REQUIREMENT 2: Determines if a voucher is a certified resubmission
 * Used for permanent badge display logic
 */
export function isCertifiedResubmission(voucher: any): boolean {
  if (!shouldShowResubmissionBadge(voucher)) {
    return false;
  }

  // Check multiple indicators for certified status
  const isCertified = voucher.certified_by || voucher.certifiedBy ||
                     voucher.status === 'VOUCHER CERTIFIED' ||
                     voucher.workflow_state === 'AUDIT_DISPATCHED' ||
                     voucher.workflow_state === 'FINANCE_CERTIFIED';

  return isCertified;
}

/**
 * REQUIREMENT 2: Gets the appropriate badge text for resubmissions
 */
export function getResubmissionBadgeText(voucher: any): string {
  if (isCertifiedResubmission(voucher)) {
    return 'CERTIFIED-RESUBMISSION';
  }
  return 'RESUBMISSION';
}

/**
 * Legacy compatibility function for existing components
 * This helps with gradual migration to the new system
 */
export function shouldShowResubmissionBadgeForTab(voucher: any, tabName: string): boolean {
  const badges = getBadgesForTab(voucher, tabName);
  return badges.some(badge =>
    badge.type === 'resubmission' || badge.type === 'certified-resubmission'
  );
}

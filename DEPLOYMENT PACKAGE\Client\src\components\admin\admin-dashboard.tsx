import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { createManagedInterval, clearManagedInterval } from '@/lib/interval-manager';
import { BackupSection } from './backup-section';
import { SystemSettingsSection } from './system-settings-section';
import { UserManagementSection } from './user-management-section';
import { PendriveBackupSection } from './pendrive-backup-section';
import { PendingRegistrationsSection } from './pending-registrations-section';
import { LiveSystemTime } from './live-system-time';
import { EnhancedRolloverSection } from './enhanced-rollover-section';
import { LANTimeManager } from './lan-time-manager';


import { Button } from '@/components/ui/button';
import { Download, Upload, Settings, <PERSON>, HardDrive, <PERSON>ota<PERSON><PERSON>c<PERSON>, Wifi } from 'lucide-react';
import { useAppStore } from '@/lib/store/hooks';
import { formatCurrentDate, formatStandardDate } from '@/lib/store/utils';

export function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const store = useAppStore();
  const { systemSettings, lastBackupDate, users, vouchers, pendingRegistrations, passwordChangeRequests, fetchPendingRegistrations, fetchPasswordChangeRequests, fetchAllUsers } = store;
  const userCount = users.length;

  // Periodically refresh pending registrations, password requests, and users
  useEffect(() => {
    // Fetch data immediately
    fetchPendingRegistrations();
    fetchPasswordChangeRequests();
    fetchAllUsers();

    // Set up interval to refresh every 5 seconds
    const intervalId = createManagedInterval(() => {
      fetchPendingRegistrations();
      fetchPasswordChangeRequests();
      fetchAllUsers();
    }, 5000);

    // Clean up interval on unmount
    return () => clearManagedInterval(intervalId);
  }, [fetchPendingRegistrations, fetchPasswordChangeRequests, fetchAllUsers]);



  // Count pending registrations and password change requests
  const pendingRegistrationsCount = pendingRegistrations.length;
  const pendingPasswordRequestsCount = passwordChangeRequests.length;
  const totalPendingRequests = pendingRegistrationsCount + pendingPasswordRequestsCount;

  const stats = [
    {
      title: 'Total Users',
      value: userCount,
      icon: <Users className="h-4 w-4 text-muted-foreground" />,
    },
    {
      title: 'Pending Requests',
      value: totalPendingRequests,
      icon: <Users className="h-4 w-4 text-muted-foreground" />,
      highlight: totalPendingRequests > 0
    },
    {
      title: 'Fiscal Year',
      value: systemSettings.currentFiscalYear,
      icon: <Settings className="h-4 w-4 text-muted-foreground" />,
    },
    {
      title: 'Last Backup',
      value: lastBackupDate ? formatStandardDate(new Date(lastBackupDate)) : 'Never',
      icon: <Download className="h-4 w-4 text-muted-foreground" />,
    },
  ];

  return (
    <div className="container mx-auto py-4">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Comprehensive system management and monitoring.
        </p>
      </div>

      {/* Live System Time */}
      <LiveSystemTime />

      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-6">
        {stats.map((stat, index) => (
          <Card key={index} className={stat.highlight ? 'border-red-500 shadow-md' : ''}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex flex-col">
                  <span className={`text-sm font-medium ${stat.highlight ? 'text-red-600 font-semibold' : 'text-muted-foreground'}`}>
                    {stat.title}
                  </span>
                  <span className={`text-2xl font-bold ${stat.highlight ? 'text-red-600' : ''}`}>
                    {stat.value}
                  </span>
                </div>
                <div className={`p-2 rounded-full ${stat.highlight ? 'bg-red-100' : 'bg-background'}`}>
                  {stat.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-3 lg:grid-cols-7 w-full md:w-auto bg-transparent">
          <TabsTrigger
            value="overview"
            className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            <Settings className="h-4 w-4" />
            <span>System Settings</span>
          </TabsTrigger>
          <TabsTrigger
            value="backup"
            className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            <Download className="h-4 w-4" />
            <span>Backup & Restore</span>
          </TabsTrigger>
          <TabsTrigger
            value="pendrive-backup"
            className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            <HardDrive className="h-4 w-4" />
            <span>Pendrive Backup</span>
          </TabsTrigger>

          <TabsTrigger
            value="users"
            className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            <Users className="h-4 w-4" />
            <span>User Management</span>
          </TabsTrigger>
          <TabsTrigger
            value="registrations"
            className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            onClick={() => pendingRegistrationsCount > 0 && setActiveTab('registrations')}
          >
            <Users className="h-4 w-4" />
            <span>Pending Registrations</span>
            {pendingRegistrationsCount > 0 && (
              <span className="ml-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                {pendingRegistrationsCount}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger
            value="rollover"
            className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            <RotateCcw className="h-4 w-4" />
            <span>Year Rollover</span>
          </TabsTrigger>
          <TabsTrigger
            value="lan-time"
            className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            <Wifi className="h-4 w-4" />
            <span>LAN Time</span>
          </TabsTrigger>

        </TabsList>
        <div className="bg-card rounded-lg border shadow">
          <TabsContent value="overview" className="p-0 m-0">
            <SystemSettingsSection />
          </TabsContent>
          <TabsContent value="backup" className="p-0 m-0">
            <BackupSection />
          </TabsContent>
          <TabsContent value="pendrive-backup" className="p-0 m-0">
            <PendriveBackupSection />
          </TabsContent>

          <TabsContent value="users" className="p-0 m-0">
            <UserManagementSection />
          </TabsContent>
          <TabsContent value="registrations" className="p-0 m-0">
            <PendingRegistrationsSection />
          </TabsContent>
          <TabsContent value="rollover" className="p-0 m-0">
            <EnhancedRolloverSection />
          </TabsContent>
          <TabsContent value="lan-time" className="p-0 m-0">
            <LANTimeManager />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
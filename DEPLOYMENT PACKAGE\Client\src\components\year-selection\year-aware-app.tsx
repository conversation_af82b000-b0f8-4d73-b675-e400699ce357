import { useState, useEffect } from 'react';
import { YearSelector } from './year-selector';
import { RolloverStatusScreen } from './rollover-status-screen';
import { useAppStore } from '@/lib/store';
import { toast } from '@/hooks/use-toast';

interface YearAwareAppProps {
  children: React.ReactNode;
  isAppInitialized: boolean;
}

export function YearAwareApp({ children, isAppInitialized }: YearAwareAppProps) {
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [isYearSelected, setIsYearSelected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isRolloverInProgress, setIsRolloverInProgress] = useState(false);
  const currentUser = useAppStore((state) => state.currentUser);

  // PRODUCTION FIX: Check year selection only after app initialization is complete
  useEffect(() => {
    const checkCurrentYear = async () => {
      try {
        console.log('🗓️ YearAwareApp: Checking current year selection...');

        // First check if rollover is in progress
        const rolloverResponse = await fetch('/api/years/rollover/status', {
          credentials: 'include'
        });

        if (rolloverResponse.ok) {
          const rolloverData = await rolloverResponse.json();
          if (rolloverData.isRolloverInProgress) {
            console.log('🗓️ Rollover in progress, showing rollover screen');
            setIsRolloverInProgress(true);
            setIsLoading(false);
            return;
          }
        }

        // Check current year selection from session
        const response = await fetch('/api/years/current', {
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          if (data.selectedYear) {
            console.log('🗓️ Year selection found in session:', data.selectedYear);
            setSelectedYear(data.selectedYear);
            setIsYearSelected(true);
            useAppStore.setState({ selectedYear: data.selectedYear });
          } else {
            console.log('🗓️ No year selection found, showing year selector');
            // No year selected - show year selector (this is normal behavior)
          }
        } else if (response.status === 401) {
          console.log('🗓️ Not authenticated, will redirect to login');
          // Not authenticated - let the auth system handle this
        } else {
          console.log('🗓️ Failed to fetch year selection, showing year selector');
          // API error - show year selector as fallback
        }
      } catch (error) {
        console.error('Error checking current year:', error);
        // On error, show year selector as fallback
      } finally {
        setIsLoading(false);
      }
    };

    // CRITICAL FIX: Only check year after app initialization is complete
    // This ensures authentication has been attempted first
    if (isAppInitialized) {
      console.log('🗓️ App initialized, checking year selection...');
      checkCurrentYear();
    } else {
      console.log('🗓️ App not yet initialized, waiting...');
    }
  }, [isAppInitialized, currentUser]);

  const handleYearSelected = async (year: number) => {
    try {
      setIsLoading(true);
      
      // Send year selection to backend
      const response = await fetch('/api/years/select', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ year })
      });

      if (response.ok) {
        const data = await response.json();
        setSelectedYear(year);
        setIsYearSelected(true);
        
        // Store year in app state
        useAppStore.setState({ selectedYear: year });
        
        // Show success message
        toast({
          title: "Year Selected",
          description: `Now working with ${year} data`,
          variant: "default"
        });

        // Refresh app data for the selected year
        const { fetchVouchers, fetchBatches, fetchAllUsers } = useAppStore.getState();
        await Promise.all([
          fetchVouchers(),
          fetchBatches(),
          fetchAllUsers()
        ]);

      } else {
        const errorData = await response.json();
        toast({
          title: "Year Selection Failed",
          description: errorData.error || "Failed to select year",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error selecting year:', error);
      toast({
        title: "Error",
        description: "Failed to select year. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleYearChange = () => {
    setIsYearSelected(false);
    setSelectedYear(null);
    useAppStore.setState({ selectedYear: null });
  };

  const handleRolloverComplete = () => {
    setIsRolloverInProgress(false);
    setIsYearSelected(false);
    setSelectedYear(null);
    // Refresh the page to reload with new year data
    window.location.reload();
  };

  // Show loading state while app is initializing or year is being checked
  if (!isAppInitialized || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg font-medium">
            {!isAppInitialized ? 'Initializing VMS...' : 'Loading VMS...'}
          </p>
        </div>
      </div>
    );
  }

  // Show rollover status screen if rollover is in progress
  if (isRolloverInProgress) {
    return <RolloverStatusScreen onRolloverComplete={handleRolloverComplete} />;
  }

  // Show year selector if no year is selected
  if (!isYearSelected || !selectedYear) {
    return <YearSelector onYearSelected={handleYearSelected} />;
  }

  // Show main app with year context
  return (
    <div className="year-aware-app">
      {/* Year indicator in top bar */}
      <div className="bg-blue-600 text-white px-4 py-2 text-sm flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="font-medium">RECORD YEAR: {selectedYear}</span>
          {selectedYear !== new Date().getFullYear() && (
            <span className="bg-blue-500 px-2 py-1 rounded text-xs">
              Historical Data
            </span>
          )}
        </div>
        <button
          onClick={handleYearChange}
          className="bg-blue-500 hover:bg-blue-400 text-white px-3 py-1 rounded text-xs font-medium transition-colors"
        >
          Change Year
        </button>
      </div>
      
      {/* Main app content */}
      {children}
    </div>
  );
}

// Hook to get current selected year
export function useSelectedYear() {
  const selectedYear = useAppStore((state) => state.selectedYear);
  return selectedYear || new Date().getFullYear();
}

// Hook to check if working with historical data
export function useIsHistoricalData() {
  const selectedYear = useSelectedYear();
  return selectedYear < new Date().getFullYear();
}

import { useState, useEffect } from 'react';
import { useAppStore } from '@/lib/store';

export function useAnalyticsData(timeRange: 'month' | 'quarter' | 'year') {
  const [isLoading, setIsLoading] = useState(true);
  const vouchers = useAppStore((state) => state.vouchers);
  const provisionalCashRecords = useAppStore((state) => state.provisionalCashRecords);
  const users = useAppStore((state) => state.users);
  const loadProvisionalCashRecords = useAppStore((state) => state.loadProvisionalCashRecords);

  const [summaryData, setSummaryData] = useState({
    totalVouchers: 0,
    vouchersTrend: 'up' as const,
    vouchersTrendValue: '+5% from last period',

    avgProcessingTime: 0,
    processingTimeTrend: 'down' as const,
    processingTimeTrendValue: '-10% from last period',

    certificationRate: 0,
    certificationRateTrend: 'up' as const,
    certificationRateTrendValue: '+2% from last period',

    totalValue: 0,
    totalValueTrend: 'up' as const,
    totalValueTrendValue: '+15% from last period',

    pendingVouchers: 0,
    pendingVouchersTrend: 'down' as const,
    pendingVouchersTrendValue: '-8% from last period',
  });

  const [savingsData, setSavingsData] = useState({
    departmental: [] as any[],
    totalSavings: 0,
    savingsPercentage: 0,
    defaultCurrency: 'GHS',
    currencySummary: {} as Record<string, { totalSavings: number; totalOriginalAmount: number; totalCertifiedAmount: number }>
  });

  const [userActivityData, setUserActivityData] = useState({
    userMetrics: [] as any[],
    activityHeatmap: [] as any[],
    topPerformer: '',
    bottomPerformer: '',
    monthlyTopPerformers: [] as any[],
    yearlyPreAuditTopPerformer: '',
    yearlyPreAuditSavings: 0,
    yearlyCertificationTopPerformer: '',
    yearlyCertificationRate: 0
  });

  const [provisionalCashData, setProvisionalCashData] = useState({
    departmentRecords: [] as any[],
    agingData: [] as any[],
    totalOutstanding: 0,
    totalCleared: 0,
    overallClearanceRate: 0
  });

  // Load provisional cash records when analytics is accessed
  useEffect(() => {
    console.log('🔄 Analytics: Loading provisional cash records...');
    loadProvisionalCashRecords().catch(error => {
      console.error('❌ Analytics: Failed to load provisional cash records:', error);
    });
  }, [loadProvisionalCashRecords]);

  // Hourly refresh for provisional cash data
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      console.log('🔄 Analytics: Hourly refresh of provisional cash records...');
      loadProvisionalCashRecords().catch(error => {
        console.error('❌ Analytics: Failed to refresh provisional cash records:', error);
      });
    }, 3600000); // Refresh every 1 hour (3600000 ms)

    return () => clearInterval(refreshInterval);
  }, [loadProvisionalCashRecords]);

  useEffect(() => {
    setIsLoading(true);

    // Calculate analytics from real data in the store

    setTimeout(() => {
      // Ensure all vouchers have currency defined to prevent undefined errors
      const safeVouchers = vouchers.map(v => ({
        ...v,
        currency: v.currency || 'GHS'
      }));

      // Calculate summary data
      const certifiedVouchers = safeVouchers.filter(v => v.status === "VOUCHER CERTIFIED");
      const rejectedVouchers = safeVouchers.filter(v => v.status === "VOUCHER REJECTED");
      const pendingVouchers = safeVouchers.filter(v =>
        v.status === "PENDING SUBMISSION" ||
        v.status === "PENDING RECEIPT" ||
        v.status === "AUDIT: PROCESSING"
      );

      const totalProcessed = certifiedVouchers.length + rejectedVouchers.length;
      const certificationRate = totalProcessed > 0
        ? Math.round((certifiedVouchers.length / totalProcessed) * 100)
        : 0;

      const totalValue = certifiedVouchers.reduce((sum, v) => sum + v.amount, 0);

      // Calculate pre-audit savings
      const savings = certifiedVouchers.reduce((sum, v) => {
        const originalAmount = v.amount;
        const certifiedAmount = v.preAuditedAmount || v.amount;
        return sum + (originalAmount - certifiedAmount);
      }, 0);

      const totalOriginalAmount = certifiedVouchers.reduce((sum, v) => sum + v.amount, 0);
      const savingsPercentage = totalOriginalAmount > 0
        ? (savings / totalOriginalAmount) * 100
        : 0;

      // Generate mock data for departmental savings with monthly breakdown
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const departments = ['FINANCE', 'MINISTRIES', 'PENSIONS', 'PENTMEDIA', 'MISSIONS', 'PENTSOS'];

      // Initialize currency tracking
      const currencies = ['GHS', 'USD', 'EUR', 'GBP', 'CFA'];
      const defaultCurrency = 'GHS';
      const currencySummary: Record<string, { totalSavings: number; totalOriginalAmount: number; totalCertifiedAmount: number }> = {};

      currencies.forEach(currency => {
        currencySummary[currency] = {
          totalSavings: 0,
          totalOriginalAmount: 0,
          totalCertifiedAmount: 0
        };
      });

      const departmentalSavings = departments.map(dept => {
        // Get vouchers for this department that have been pre-audited
        const deptVouchers = safeVouchers.filter(v =>
          v.originalDepartment === dept &&
          v.preAuditedAmount &&
          v.amount
        );

        // Determine the most common currency for this department
        const currencyCount = {};
        deptVouchers.forEach(v => {
          const currency = v.currency || 'GHS';
          currencyCount[currency] = (currencyCount[currency] || 0) + 1;
        });
        const deptCurrency = Object.keys(currencyCount).length > 0
          ? Object.keys(currencyCount).reduce((a, b) =>
              currencyCount[a] > currencyCount[b] ? a : b, 'GHS'
            )
          : 'GHS';

        // Calculate monthly savings for this department
        const monthlySavings = months.map((month, monthIndex) => {
          const monthVouchers = deptVouchers.filter(v => {
            const voucherDate = new Date(v.createdAt);
            return voucherDate.getMonth() === monthIndex;
          });

          const originalAmount = monthVouchers.reduce((sum, v) => sum + (parseFloat(v.amount) || 0), 0);
          const certifiedAmount = monthVouchers.reduce((sum, v) => sum + (parseFloat(v.preAuditedAmount) || 0), 0);
          const savings = originalAmount - certifiedAmount;
          const percentage = originalAmount > 0 ? (savings / originalAmount) * 100 : 0;

          return {
            month,
            savings: Math.round(savings || 0),
            percentage: Math.round((percentage || 0) * 10) / 10,
            originalAmount: Math.round(originalAmount || 0),
            certifiedAmount: Math.round(certifiedAmount || 0),
            currency: deptCurrency
          };
        });

        // Calculate yearly totals for this department
        const yearlyOriginalAmount = monthlySavings.reduce((sum, m) => sum + m.originalAmount, 0);
        const yearlyCertifiedAmount = monthlySavings.reduce((sum, m) => sum + m.certifiedAmount, 0);
        const yearlySavings = yearlyOriginalAmount - yearlyCertifiedAmount;
        const yearlyPercentage = yearlyOriginalAmount > 0 ? (yearlySavings / yearlyOriginalAmount) * 100 : 0;

        // Update the global currency summary
        if (!currencySummary[deptCurrency]) {
          currencySummary[deptCurrency] = {
            totalSavings: 0,
            totalOriginalAmount: 0,
            totalCertifiedAmount: 0
          };
        }
        currencySummary[deptCurrency].totalSavings += yearlySavings;
        currencySummary[deptCurrency].totalOriginalAmount += yearlyOriginalAmount;
        currencySummary[deptCurrency].totalCertifiedAmount += yearlyCertifiedAmount;

        return {
          name: dept,
          totalSavings: Math.round(yearlySavings || 0),
          savingsPercentage: Math.round((yearlyPercentage || 0) * 10) / 10,
          totalOriginalAmount: Math.round(yearlyOriginalAmount || 0),
          totalCertifiedAmount: Math.round(yearlyCertifiedAmount || 0),
          currency: deptCurrency,
          monthlySavings,
          // Add these for compatibility with the table component
          savings: Math.round(yearlySavings || 0),
          percentage: Math.round((yearlyPercentage || 0) * 10) / 10,
          originalAmount: Math.round(yearlyOriginalAmount || 0),
          certifiedAmount: Math.round(yearlyCertifiedAmount || 0)
        };
      });

      // Calculate real user activity metrics from actual voucher data
      const auditUsers = users.filter(u => u.department === 'AUDIT').map(u => u.name);
      const userMetrics = auditUsers.map(name => {
        // Get vouchers processed by this user
        const userVouchers = safeVouchers.filter(v =>
          v.preAuditedBy === name || v.certifiedBy === name || v.rejectedBy === name
        );

        const vouchersProcessed = userVouchers.length;

        // Calculate pre-audit savings for this user
        const preAuditVouchers = safeVouchers.filter(v => v.preAuditedBy === name && v.preAuditedAmount);
        const preAuditSavings = preAuditVouchers.reduce((sum, v) => {
          const originalAmount = parseFloat(v.amount) || 0;
          const preAuditedAmount = parseFloat(v.preAuditedAmount) || 0;
          return sum + (originalAmount - preAuditedAmount);
        }, 0);

        const totalOriginalAmount = preAuditVouchers.reduce((sum, v) => sum + (parseFloat(v.amount) || 0), 0);
        const preAuditSavingsPercentage = totalOriginalAmount > 0 ? (preAuditSavings / totalOriginalAmount) * 100 : 0;

        // Calculate certification and rejection rates
        const certifiedVouchers = safeVouchers.filter(v => v.certifiedBy === name).length;
        const rejectedVouchers = safeVouchers.filter(v => v.rejectedBy === name).length;
        const totalProcessed = certifiedVouchers + rejectedVouchers;
        const certificationRate = totalProcessed > 0 ? (certifiedVouchers / totalProcessed) * 100 : 0;
        const rejectionRate = totalProcessed > 0 ? (rejectedVouchers / totalProcessed) * 100 : 0;

        // Calculate average processing time (simplified - using creation to certification time)
        const processedVouchers = safeVouchers.filter(v => v.certifiedBy === name && v.certifiedAt);
        const avgProcessingTime = processedVouchers.length > 0
          ? processedVouchers.reduce((sum, v) => {
              const created = new Date(v.createdAt).getTime();
              const certified = new Date(v.certifiedAt).getTime();
              return sum + ((certified - created) / (1000 * 60 * 60)); // hours
            }, 0) / processedVouchers.length
          : 0;

        return {
          name,
          vouchersProcessed: vouchersProcessed || 0,
          avgProcessingTime: Math.round((avgProcessingTime || 0) * 10) / 10, // Round to 1 decimal
          certificationRate: Math.round((certificationRate || 0) * 10) / 10,
          rejectionRate: Math.round((rejectionRate || 0) * 10) / 10,
          preAuditSavings: Math.round(preAuditSavings || 0),
          preAuditSavingsPercentage: Math.round((preAuditSavingsPercentage || 0) * 10) / 10
        };
      });

      // Sort users by vouchers processed to find top and bottom performers
      const sortedUsers = [...userMetrics].sort((a, b) => b.vouchersProcessed - a.vouchersProcessed);
      const topPerformer = sortedUsers[0]?.name || 'N/A';
      const bottomPerformer = sortedUsers[sortedUsers.length - 1]?.name || 'N/A';

      // Find yearly top performers
      const sortedByPreAudit = [...userMetrics].sort((a, b) => b.preAuditSavings - a.preAuditSavings);
      const yearlyPreAuditTopPerformer = sortedByPreAudit[0]?.name || 'N/A';
      const yearlyPreAuditSavings = sortedByPreAudit[0]?.preAuditSavings || 0;

      const sortedByCertification = [...userMetrics].sort((a, b) => b.certificationRate - a.certificationRate);
      const yearlyCertificationTopPerformer = sortedByCertification[0]?.name || 'N/A';
      const yearlyCertificationRate = sortedByCertification[0]?.certificationRate || 0;

      // Calculate real monthly top performers from actual data
      const monthlyTopPerformers = months.map(month => {
        const monthIndex = months.indexOf(month);
        const monthStart = new Date(new Date().getFullYear(), monthIndex, 1);
        const monthEnd = new Date(new Date().getFullYear(), monthIndex + 1, 0);

        // Get vouchers for this month
        const monthVouchers = safeVouchers.filter(v => {
          const voucherDate = new Date(v.createdAt);
          return voucherDate >= monthStart && voucherDate <= monthEnd;
        });

        // Calculate pre-audit savings by user for this month
        const userPreAuditSavings = auditUsers.map(name => {
          const userMonthVouchers = monthVouchers.filter(v => v.preAuditedBy === name && v.preAuditedAmount);
          const savings = userMonthVouchers.reduce((sum, v) => {
            const originalAmount = parseFloat(v.amount) || 0;
            const preAuditedAmount = parseFloat(v.preAuditedAmount) || 0;
            return sum + (originalAmount - preAuditedAmount);
          }, 0);
          return { name, savings };
        }).sort((a, b) => b.savings - a.savings);

        // Calculate certification rates by user for this month
        const userCertificationRates = auditUsers.map(name => {
          const certified = monthVouchers.filter(v => v.certifiedBy === name).length;
          const rejected = monthVouchers.filter(v => v.rejectedBy === name).length;
          const total = certified + rejected;
          const rate = total > 0 ? (certified / total) * 100 : 0;
          return { name, rate };
        }).sort((a, b) => b.rate - a.rate);

        return {
          month,
          preAuditTopPerformer: userPreAuditSavings[0]?.name || 'N/A',
          preAuditSavings: Math.round(userPreAuditSavings[0]?.savings || 0),
          certificationTopPerformer: userCertificationRates[0]?.name || 'N/A',
          certificationRate: Math.round((userCertificationRates[0]?.rate || 0) * 10) / 10
        };
      });

      // Calculate real activity heatmap from voucher processing times
      const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
      const activityHeatmap = days.map(day => {
        const dayIndex = days.indexOf(day) + 1; // Monday = 1, Sunday = 0

        // Get vouchers processed on this day of the week
        const dayVouchers = safeVouchers.filter(v => {
          if (!v.certifiedAt && !v.rejectedAt) return false;
          const processDate = new Date(v.certifiedAt || v.rejectedAt);
          return processDate.getDay() === dayIndex;
        });

        // Calculate peak hour (hour with most activity)
        const hourCounts = {};
        dayVouchers.forEach(v => {
          const processDate = new Date(v.certifiedAt || v.rejectedAt);
          const hour = processDate.getHours();
          hourCounts[hour] = (hourCounts[hour] || 0) + 1;
        });

        const peakHour = Object.keys(hourCounts).reduce((a, b) =>
          hourCounts[a] > hourCounts[b] ? a : b, '9'
        );

        return {
          day,
          hour: parseInt(peakHour) || 9,
          value: dayVouchers.length
        };
      });

      // Calculate real provisional cash analysis from actual records with currency grouping
      const provisionalCashCurrencySummary = {};
      const departmentRecords = departments.map(department => {
        // Get provisional cash records for this department
        const deptRecords = provisionalCashRecords.filter(record => {
          // Find the voucher for this record to get its original department
          const voucher = vouchers.find(v => v.id === record.voucherId);
          return voucher?.originalDepartment === department;
        });

        // Group by currency for this department
        const currencyData = {};
        deptRecords.forEach(record => {
          const currency = record.currency || 'GHS';
          if (!currencyData[currency]) {
            currencyData[currency] = {
              total: 0,
              cleared: 0,
              outstanding: 0,
              clearanceRate: 0,
              transactions: []
            };
          }

          const amount = record.mainAmount || 0;
          const clearedAmount = (record.clearanceRemark && record.clearanceRemark !== 'RETURNED')
            ? (record.amountRetired || record.mainAmount || 0)
            : 0;

          currencyData[currency].total += amount;
          currencyData[currency].cleared += clearedAmount;
          currencyData[currency].outstanding = currencyData[currency].total - currencyData[currency].cleared;
          currencyData[currency].clearanceRate = currencyData[currency].total > 0
            ? Math.round((currencyData[currency].cleared / currencyData[currency].total) * 100)
            : 0;

          // Add to global currency summary
          if (!provisionalCashCurrencySummary[currency]) {
            provisionalCashCurrencySummary[currency] = {
              totalOutstanding: 0,
              totalCleared: 0,
              totalAmount: 0
            };
          }
          provisionalCashCurrencySummary[currency].totalAmount += amount;
          provisionalCashCurrencySummary[currency].totalCleared += clearedAmount;
          provisionalCashCurrencySummary[currency].totalOutstanding += (amount - clearedAmount);

          // Convert to transaction format
          currencyData[currency].transactions.push({
            id: record.id,
            voucherId: record.voucherRef,
            claimant: record.claimant,
            amount: record.mainAmount,
            currency: currency,
            date: record.date,
            status: (record.clearanceRemark && record.clearanceRemark !== 'RETURNED') ? 'cleared' : 'not-cleared'
          });
        });

        // Calculate legacy totals (for backward compatibility)
        const total = deptRecords.reduce((sum, record) => sum + (record.mainAmount || 0), 0);
        const cleared = deptRecords
          .filter(record => record.clearanceRemark && record.clearanceRemark !== 'RETURNED')
          .reduce((sum, record) => sum + (record.amountRetired || record.mainAmount || 0), 0);
        const outstanding = total - cleared;
        const clearanceRate = total > 0 ? Math.round((cleared / total) * 100) : 0;

        // Get total certified amount for this department
        const deptVouchers = safeVouchers.filter(v => v.originalDepartment === department && v.certifiedAmount);
        const totalCertifiedAmount = deptVouchers.reduce((sum, v) => sum + (parseFloat(v.certifiedAmount) || 0), 0);

        // Convert all records to transaction format (for legacy support)
        const transactions = deptRecords.map(record => ({
          id: record.id,
          voucherId: record.voucherRef,
          claimant: record.claimant,
          amount: record.mainAmount,
          currency: record.currency || 'GHS',
          date: record.date,
          status: (record.clearanceRemark && record.clearanceRemark !== 'RETURNED') ? 'cleared' : 'not-cleared'
        }));

        return {
          department,
          outstanding: Math.round(outstanding || 0),
          cleared: Math.round(cleared || 0),
          total: Math.round(total || 0),
          clearanceRate: clearanceRate || 0,
          totalCertifiedAmount: Math.round(totalCertifiedAmount || 0),
          transactions,
          currencyData // NEW: Currency-specific data
        };
      });

      // Calculate aging data based on actual record dates
      const agingRanges = ['0-30 days', '31-60 days', '61-90 days', '91+ days'];
      const now = new Date();
      const agingData = agingRanges.map(range => {
        let startDays, endDays;

        switch (range) {
          case '0-30 days':
            startDays = 0; endDays = 30;
            break;
          case '31-60 days':
            startDays = 31; endDays = 60;
            break;
          case '61-90 days':
            startDays = 61; endDays = 90;
            break;
          case '91+ days':
            startDays = 91; endDays = Infinity;
            break;
          default:
            startDays = 0; endDays = 30;
        }

        const rangeRecords = provisionalCashRecords.filter(record => {
          if (record.clearanceRemark && record.clearanceRemark !== 'RETURNED') return false; // Skip cleared records

          const recordDate = new Date(record.date);
          const daysDiff = Math.floor((now.getTime() - recordDate.getTime()) / (1000 * 60 * 60 * 24));

          return daysDiff >= startDays && (endDays === Infinity || daysDiff <= endDays);
        });

        const value = rangeRecords.reduce((sum, record) => sum + (record.mainAmount || 0), 0);
        const count = rangeRecords.length;

        return {
          range,
          value: Math.round(value),
          count
        };
      });

      // Calculate totals for provisional cash
      const totalOutstanding = departmentRecords.reduce((sum, record) => sum + (record.outstanding || 0), 0);
      const totalCleared = departmentRecords.reduce((sum, record) => sum + (record.cleared || 0), 0);
      const totalAmount = totalOutstanding + totalCleared;
      const overallClearanceRate = totalAmount > 0 ? Math.round((totalCleared / totalAmount) * 100) : 0;

      // Calculate real average processing time from actual data
      const processedVouchers = safeVouchers.filter(v => v.certifiedAt && v.createdAt);
      const avgProcessingTime = processedVouchers.length > 0
        ? processedVouchers.reduce((sum, v) => {
            const created = new Date(v.createdAt).getTime();
            const certified = new Date(v.certifiedAt).getTime();
            return sum + ((certified - created) / (1000 * 60 * 60)); // hours
          }, 0) / processedVouchers.length
        : 0;

      // Calculate trends based on current vs previous period (simplified)
      const currentMonth = new Date().getMonth();
      const currentMonthVouchers = safeVouchers.filter(v => new Date(v.createdAt).getMonth() === currentMonth);
      const previousMonthVouchers = safeVouchers.filter(v => new Date(v.createdAt).getMonth() === currentMonth - 1);

      const vouchersTrend = currentMonthVouchers.length >= previousMonthVouchers.length ? 'up' : 'down';
      const vouchersChange = previousMonthVouchers.length > 0
        ? Math.round(((currentMonthVouchers.length - previousMonthVouchers.length) / previousMonthVouchers.length) * 100)
        : 0;

      // Update state with calculated data
      setSummaryData({
        totalVouchers: totalProcessed,
        vouchersTrend,
        vouchersTrendValue: `${vouchersChange >= 0 ? '+' : ''}${vouchersChange}% from last period`,

        avgProcessingTime: Math.round(avgProcessingTime * 10) / 10,
        processingTimeTrend: 'stable', // Would need historical data for proper trend
        processingTimeTrendValue: 'Based on current data',

        certificationRate,
        certificationRateTrend: certificationRate >= 85 ? 'up' : 'down',
        certificationRateTrendValue: 'Based on current performance',

        totalValue,
        totalValueTrend: 'up',
        totalValueTrendValue: 'Based on current vouchers',

        pendingVouchers: pendingVouchers.length,
        pendingVouchersTrend: pendingVouchers.length <= 10 ? 'down' : 'up',
        pendingVouchersTrendValue: `${pendingVouchers.length} vouchers pending`,
      });

      setSavingsData({
        departmental: departmentalSavings,
        totalSavings: Math.round(savings || 0),
        savingsPercentage: Math.round((savingsPercentage || 0) * 10) / 10,
        defaultCurrency: defaultCurrency,
        currencySummary: currencySummary
      });

      setUserActivityData({
        userMetrics,
        activityHeatmap,
        topPerformer,
        bottomPerformer,
        monthlyTopPerformers,
        yearlyPreAuditTopPerformer,
        yearlyPreAuditSavings,
        yearlyCertificationTopPerformer,
        yearlyCertificationRate
      });

      setProvisionalCashData({
        departmentRecords,
        agingData,
        totalOutstanding: Math.round(totalOutstanding || 0),
        totalCleared: Math.round(totalCleared || 0),
        overallClearanceRate: overallClearanceRate || 0,
        currencySummary: provisionalCashCurrencySummary
      });

      setIsLoading(false);
    }, 1000);
  }, [vouchers, users, provisionalCashRecords, timeRange]);

  return {
    summaryData,
    savingsData,
    userActivityData,
    provisionalCashData,
    isLoading
  };
}

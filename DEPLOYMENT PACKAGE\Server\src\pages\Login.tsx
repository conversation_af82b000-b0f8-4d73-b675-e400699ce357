import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/lib/store';
import { usersApi } from '@/lib/api';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Department } from '@/lib/types';
import { departments } from '@/lib/data';
import { toast } from 'sonner';
import { PasswordChangeRequestDialog } from '@/components/change-password-dialog';
import { LogoSignature } from '@/components/logo-signature';
import { Eye, EyeOff, AlertTriangle, X } from 'lucide-react';
import { ModeToggle } from '@/components/mode-toggle';

export default function Login() {
  const navigate = useNavigate();
  const login = useAppStore((state) => state.login);

  const [department, setDepartment] = useState<Department | ''>('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [departmentUsers, setDepartmentUsers] = useState<any[]>([]);
  const [sessionMessage, setSessionMessage] = useState<string | null>(null);
  const [sessionMessageType, setSessionMessageType] = useState<'expired' | 'multipleLogin' | null>(null);
  const [showPasswordChangeDialog, setShowPasswordChangeDialog] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // Check for simple auth errors
  useEffect(() => {
    const authError = localStorage.getItem('auth_error');
    if (authError) {
      setSessionMessage(authError);
      setSessionMessageType('expired');
      localStorage.removeItem('auth_error');
    }
  }, []);

  // PRODUCTION FIX: Fetch users and setup real-time updates via SSE + polling fallback
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        // Add timestamp to prevent caching and ensure fresh data
        const timestamp = Date.now();
        const response = await fetch(`/api/auth/users-by-department?_t=${timestamp}`);
        if (response.ok) {
          const users = await response.json();
          setAllUsers(users);
          console.log(`[${new Date().toLocaleTimeString()}] Fetched users for login:`, users.length);
        } else {
          console.error('Failed to fetch users:', response.status, response.statusText);
          // Fallback to basic department structure - no hardcoded names
          setAllUsers([
            { id: 'admin', name: 'SYSTEM ADMINISTRATOR', department: 'SYSTEM ADMIN' }
          ]);
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        // Fallback to basic department structure - no hardcoded names
        setAllUsers([
          { id: 'admin', name: 'SYSTEM ADMINISTRATOR', department: 'SYSTEM ADMIN' }
        ]);
      }
    };

    // PRODUCTION: Fetch users on mount
    fetchUsers();

    // REAL-TIME: Setup Server-Sent Events for instant updates
    try {
      const eventSource = new EventSource('/api/login-updates/user-updates');
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('🎉 LOGIN SSE: Connected to real-time user updates');
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('🔔 LOGIN SSE: Received update:', data);

          if (data.type === 'user_approved' || data.type === 'connected') {
            // Refresh user list immediately when new user is approved
            console.log('🔄 LOGIN SSE: Refreshing user list due to approval');
            fetchUsers();
          }
        } catch (error) {
          console.error('LOGIN SSE: Error parsing message:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.warn('LOGIN SSE: Connection error, falling back to polling:', error);
        eventSource.close();
        eventSourceRef.current = null;

        // Fallback to polling if SSE fails
        if (!pollingIntervalRef.current) {
          pollingIntervalRef.current = setInterval(() => {
            console.log('[LOGIN POLLING] Checking for user updates...');
            fetchUsers();
          }, 5000);
        }
      };
    } catch (error) {
      console.warn('LOGIN SSE: Failed to setup SSE, using polling fallback:', error);

      // Fallback to polling
      pollingIntervalRef.current = setInterval(() => {
        console.log('[LOGIN POLLING] Checking for user updates...');
        fetchUsers();
      }, 5000);
    }

    // Cleanup on unmount
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, []);

  // Filter users by department when department changes
  useEffect(() => {
    if (department) {
      const filteredUsers = allUsers.filter(user =>
        user.department.toUpperCase() === department.toUpperCase()
      );
      setDepartmentUsers(filteredUsers);
    } else {
      setDepartmentUsers([]);
    }
  }, [department, allUsers]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if all required fields are filled
    if (!department || !password) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Make sure username is selected
    if (!username) {
      toast.error('Please select a username');
      return;
    }

    setIsLoading(true);

    try {
      // Use backend authentication
      const success = await login(department as Department, username, password, false);

      if (success) {
        // Get the current user from the store
        const currentUser = useAppStore.getState().currentUser;

        // Route based on department
        if (currentUser) {
          if (currentUser.department === 'AUDIT') {
            navigate('/audit-dashboard');
          } else if (currentUser.department === 'SYSTEM ADMIN') {
            navigate('/admin-dashboard');
          } else {
            navigate('/dashboard');
          }
        }
      } else {
        toast.error('Invalid credentials. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-slate-50 dark:bg-slate-900 relative">
      <div className="absolute top-4 right-4">
        <ModeToggle />
      </div>
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            VOUCHER MANAGEMENT SYSTEM
          </CardTitle>
          <CardDescription className="text-center">
            Enter your department credentials
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleLogin}>
          <CardContent className="space-y-4">
            {/* Session Expiry Notification */}
            {sessionMessage && (
              <Alert className={`border-2 ${sessionMessageType === 'expired' ? 'border-red-500 bg-red-50 dark:bg-red-950' : 'border-orange-500 bg-orange-50 dark:bg-orange-950'}`}>
                <AlertTriangle className={`h-4 w-4 ${sessionMessageType === 'expired' ? 'text-red-600' : 'text-orange-600'}`} />
                <AlertDescription className={`${sessionMessageType === 'expired' ? 'text-red-800 dark:text-red-200' : 'text-orange-800 dark:text-orange-200'} pr-8`}>
                  {sessionMessage}
                </AlertDescription>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-2 h-6 w-6 p-0"
                  onClick={() => {
                    setSessionMessage(null);
                    setSessionMessageType(null);
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="department">Department</Label>
              <Select
                value={department}
                onValueChange={(value: string) => {
                  setDepartment(value as Department);
                  setUsername('');
                }}
              >
                <SelectTrigger id="department">
                  <SelectValue placeholder="Select your department" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Select
                value={username}
                onValueChange={setUsername}
                disabled={!department}
              >
                <SelectTrigger id="username">
                  <SelectValue placeholder="Select your username" />
                </SelectTrigger>
                <SelectContent>
                  {departmentUsers.map((user) => (
                    <SelectItem key={user.id} value={user.name}>
                      {user.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2"
                  onClick={() => setShowPassword(!showPassword)}
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <Button className="w-full" type="submit" disabled={isLoading}>
              {isLoading ? 'Signing in...' : 'Sign In'}
            </Button>



            <div className="text-center text-sm space-y-2">
              <div>
                <span className="text-muted-foreground">Need access? </span>
                <Button variant="link" className="p-0" onClick={() => navigate('/register')}>
                  Request Account
                </Button>
              </div>
              <div>
                <span className="text-muted-foreground">Forgot password? </span>
                <Button variant="link" className="p-0" onClick={() => setShowPasswordChangeDialog(true)}>
                  Request Password Change
                </Button>
              </div>
            </div>
          </CardFooter>
        </form>
      </Card>

      <div className="mt-8 flex flex-col items-center space-y-2">
        <LogoSignature size="md" variant="light" />
        <div className="text-xs text-muted-foreground">
          © {new Date().getFullYear()} VMS (Voucher Management System). All rights reserved.
        </div>
      </div>

      {/* Password Change Request Dialog */}
      <PasswordChangeRequestDialog
        open={showPasswordChangeDialog}
        onClose={() => setShowPasswordChangeDialog(false)}
      />
    </div>
  );
}

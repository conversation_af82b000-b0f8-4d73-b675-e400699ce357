// Real-Time Sync Manager - Production Fix for Voucher Display After Offline Sync
// Ensures vouchers appear immediately in UI after sync without breaking existing functions

import { useAppStore } from './store';
import { connectionManager } from './connection-resilience';
import { toast } from 'sonner';

interface SyncEvent {
  type: 'VOUCHER_SYNCED' | 'BATCH_SYNCED' | 'STATUS_UPDATED';
  data: any;
  timestamp: number;
}

class RealTimeSyncManager {
  private syncEventQueue: SyncEvent[] = [];
  private isProcessingEvents: boolean = false;
  private uiRefreshCallbacks: Map<string, () => void> = new Map();
  private lastSyncTimestamp: number = 0;

  constructor() {
    this.initializeEventHandling();
  }

  private initializeEventHandling() {
    // Listen for connection restoration
    window.addEventListener('online', this.handleConnectionRestored.bind(this));
    
    // Listen for custom sync events
    window.addEventListener('vms-sync-complete', this.handleSyncComplete.bind(this));
    
    console.log('🔄 Real-time sync manager initialized');
  }

  // Register UI refresh callbacks for different components
  public registerRefreshCallback(componentId: string, callback: () => void) {
    this.uiRefreshCallbacks.set(componentId, callback);
    console.log(`📝 Registered refresh callback for ${componentId}`);
  }

  // Unregister callback when component unmounts
  public unregisterRefreshCallback(componentId: string) {
    this.uiRefreshCallbacks.delete(componentId);
    console.log(`🗑️ Unregistered refresh callback for ${componentId}`);
  }

  // Handle connection restoration
  private async handleConnectionRestored() {
    console.log('🌐 Connection restored - preparing for real-time sync');
    
    // Wait for connection to stabilize
    await this.delay(2000);
    
    // Check if sync is needed
    const pendingCount = connectionManager.getPendingOperationsCount();
    if (pendingCount > 0) {
      console.log(`🔄 ${pendingCount} operations pending sync - monitoring for completion`);
      this.monitorSyncProgress();
    }
  }

  // Monitor sync progress and trigger UI updates
  private async monitorSyncProgress() {
    const maxWaitTime = 30000; // 30 seconds max wait
    const checkInterval = 1000; // Check every second
    let waitTime = 0;
    
    const checkSync = async () => {
      const pendingCount = connectionManager.getPendingOperationsCount();
      
      if (pendingCount === 0) {
        console.log('✅ All operations synced - triggering real-time UI refresh');
        await this.triggerRealTimeRefresh();
        return true;
      }
      
      waitTime += checkInterval;
      if (waitTime >= maxWaitTime) {
        console.log('⏰ Sync monitoring timeout - forcing UI refresh');
        await this.triggerRealTimeRefresh();
        return true;
      }
      
      return false;
    };
    
    // Start monitoring
    const monitor = setInterval(async () => {
      const completed = await checkSync();
      if (completed) {
        clearInterval(monitor);
      }
    }, checkInterval);
  }

  // Handle sync completion event
  private async handleSyncComplete(event: CustomEvent) {
    console.log('🎯 Sync completion event received:', event.detail);
    
    const { syncedCount, failedCount } = event.detail;
    
    if (syncedCount > 0) {
      console.log(`✅ ${syncedCount} operations synced successfully - triggering UI refresh`);
      await this.triggerRealTimeRefresh();
      
      // Show success notification
      toast.success(`✅ ${syncedCount} operations synced successfully`);
    }
    
    if (failedCount > 0) {
      console.log(`❌ ${failedCount} operations failed to sync`);
      toast.error(`❌ ${failedCount} operations failed to sync`);
    }
  }

  // Trigger comprehensive real-time UI refresh
  private async triggerRealTimeRefresh() {
    if (this.isProcessingEvents) {
      console.log('🔄 Already processing refresh - skipping duplicate');
      return;
    }
    
    this.isProcessingEvents = true;
    this.lastSyncTimestamp = Date.now();
    
    try {
      console.log('🔄 Starting comprehensive real-time UI refresh...');
      
      // 1. Refresh store data from server
      await this.refreshStoreData();
      
      // 2. Trigger component-specific refreshes
      await this.triggerComponentRefreshes();
      
      // 3. Force re-render of voucher lists
      await this.forceVoucherListRefresh();
      
      // 4. Update connection status
      this.updateConnectionStatus();
      
      console.log('✅ Real-time UI refresh completed successfully');
      
    } catch (error) {
      console.error('❌ Real-time UI refresh failed:', error);
      toast.error('UI refresh failed - please manually refresh page');
    } finally {
      this.isProcessingEvents = false;
    }
  }

  // Refresh store data from server
  private async refreshStoreData() {
    console.log('📊 Refreshing store data from server...');
    
    const store = useAppStore.getState();
    
    try {
      // Refresh vouchers
      if (store.fetchVouchers) {
        await store.fetchVouchers();
        console.log('✅ Vouchers refreshed');
      }
      
      // Refresh batches
      if (store.fetchBatches) {
        await store.fetchBatches();
        console.log('✅ Batches refreshed');
      }
      
      // Refresh user data if needed
      if (store.fetchUsers) {
        await store.fetchUsers();
        console.log('✅ Users refreshed');
      }
      
    } catch (error) {
      console.error('❌ Store data refresh failed:', error);
      throw error;
    }
  }

  // Trigger component-specific refreshes
  private async triggerComponentRefreshes() {
    console.log('🔄 Triggering component refreshes...');
    
    let refreshCount = 0;
    
    for (const [componentId, callback] of this.uiRefreshCallbacks.entries()) {
      try {
        console.log(`🔄 Refreshing component: ${componentId}`);
        callback();
        refreshCount++;
      } catch (error) {
        console.error(`❌ Failed to refresh component ${componentId}:`, error);
      }
    }
    
    console.log(`✅ Refreshed ${refreshCount} components`);
  }

  // Force voucher list refresh by triggering state updates
  private async forceVoucherListRefresh() {
    console.log('📋 Forcing voucher list refresh...');
    
    const store = useAppStore.getState();
    
    // Force re-render by updating a timestamp
    if (store.setLastRefreshTime) {
      store.setLastRefreshTime(Date.now());
    }
    
    // Dispatch custom event for voucher list components
    window.dispatchEvent(new CustomEvent('vms-vouchers-updated', {
      detail: { timestamp: Date.now(), source: 'sync' }
    }));
    
    console.log('✅ Voucher list refresh triggered');
  }

  // Update connection status indicators
  private updateConnectionStatus() {
    console.log('📡 Updating connection status...');
    
    // Dispatch connection status update
    window.dispatchEvent(new CustomEvent('vms-connection-status', {
      detail: { 
        status: 'synced', 
        timestamp: Date.now(),
        lastSyncTime: this.lastSyncTimestamp
      }
    }));
  }

  // Public method to manually trigger refresh
  public async manualRefresh() {
    console.log('🔄 Manual refresh triggered');
    await this.triggerRealTimeRefresh();
  }

  // Public method to add sync event
  public addSyncEvent(event: SyncEvent) {
    this.syncEventQueue.push(event);
    console.log(`📝 Added sync event: ${event.type}`);
    
    // Process events if not already processing
    if (!this.isProcessingEvents) {
      this.processSyncEvents();
    }
  }

  // Process queued sync events
  private async processSyncEvents() {
    if (this.syncEventQueue.length === 0) return;
    
    console.log(`🔄 Processing ${this.syncEventQueue.length} sync events`);
    
    const events = [...this.syncEventQueue];
    this.syncEventQueue = [];
    
    for (const event of events) {
      try {
        await this.processSyncEvent(event);
      } catch (error) {
        console.error(`❌ Failed to process sync event ${event.type}:`, error);
      }
    }
  }

  // Process individual sync event
  private async processSyncEvent(event: SyncEvent) {
    switch (event.type) {
      case 'VOUCHER_SYNCED':
        console.log('📄 Processing voucher sync event');
        await this.handleVoucherSynced(event.data);
        break;
        
      case 'BATCH_SYNCED':
        console.log('📦 Processing batch sync event');
        await this.handleBatchSynced(event.data);
        break;
        
      case 'STATUS_UPDATED':
        console.log('🔄 Processing status update event');
        await this.handleStatusUpdated(event.data);
        break;
        
      default:
        console.warn(`❓ Unknown sync event type: ${event.type}`);
    }
  }

  // Handle voucher synced event
  private async handleVoucherSynced(voucherData: any) {
    console.log('📄 Voucher synced - updating UI:', voucherData.id);
    
    // Trigger specific voucher refresh
    window.dispatchEvent(new CustomEvent('vms-voucher-synced', {
      detail: voucherData
    }));
  }

  // Handle batch synced event
  private async handleBatchSynced(batchData: any) {
    console.log('📦 Batch synced - updating UI:', batchData.id);
    
    // Trigger batch refresh
    window.dispatchEvent(new CustomEvent('vms-batch-synced', {
      detail: batchData
    }));
  }

  // Handle status updated event
  private async handleStatusUpdated(statusData: any) {
    console.log('🔄 Status updated - updating UI:', statusData);
    
    // Trigger status refresh
    window.dispatchEvent(new CustomEvent('vms-status-updated', {
      detail: statusData
    }));
  }

  // Utility delay function
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get sync statistics
  public getSyncStats() {
    return {
      lastSyncTimestamp: this.lastSyncTimestamp,
      queuedEvents: this.syncEventQueue.length,
      registeredCallbacks: this.uiRefreshCallbacks.size,
      isProcessing: this.isProcessingEvents
    };
  }
}

// Export singleton instance
export const realTimeSyncManager = new RealTimeSyncManager();

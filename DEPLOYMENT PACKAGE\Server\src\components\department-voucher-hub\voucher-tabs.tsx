
import React from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Department, Voucher } from '@/lib/types';
import { Badge } from '@/components/ui/badge';

interface VoucherTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  department: Department;
  // Legacy voucher arrays (for backward compatibility)
  newVouchers?: Voucher[];
  pendingDispatchVouchers?: Voucher[];
  dispatchedVouchers?: Voucher[];
  returnedVouchers?: Voucher[];
  rejectedVouchers?: Voucher[];
  // NEW: Workflow-aware props
  tabCounts?: Record<string, number>;
  getAvailableTabs?: () => string[];
  getTabDisplayName?: (tabName: string) => string;
}

export function DepartmentVoucherTabs({
  activeTab,
  setActiveTab,
  department,
  newVouchers = [],
  pendingDispatchVouchers = [],
  dispatchedVouchers = [],
  returnedVouchers = [],
  rejectedVouchers = [],
  tabCounts,
  getAvailableTabs,
  getTabDisplayName
}: VoucherTabsProps) {



  // DEPARTMENT VOUCHER HUBS: Always use the legacy department tab system
  // The workflow system is designed for Finance Dashboard, not Department Voucher Hubs
  const tabs = React.useMemo(() => {
    return [
      {
        id: 'new-vouchers',
        label: 'NEW VOUCHERS',
        count: newVouchers.length
      },
      {
        id: 'pending-dispatch',
        label: 'PENDING DISPATCH',
        count: pendingDispatchVouchers.length
      },
      {
        id: 'dispatched',
        label: 'DISPATCHED',
        count: dispatchedVouchers.length
      },
      {
        id: 'returned-vouchers',
        label: 'RETURNED',
        count: returnedVouchers.length
      },
      {
        id: 'rejected-vouchers',
        label: 'REJECTED',
        count: rejectedVouchers.length
      }
    ];
  }, [newVouchers, pendingDispatchVouchers, dispatchedVouchers, returnedVouchers, rejectedVouchers]);



  return (
    <div>

      <Tabs defaultValue="new-vouchers" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className={`grid w-full grid-cols-${tabs.length}`}>
          {tabs.map(tab => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="whitespace-nowrap text-xs px-2 py-1 sm:text-sm sm:px-4 sm:py-2 flex items-center gap-2"
            >
              <span>{tab.label}</span>
              <Badge
                variant={tab.count > 0 ? "default" : "secondary"}
                className="text-xs px-1.5 py-0.5"
              >
                {tab.count}
              </Badge>
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
}

// Export as VoucherTabs for backward compatibility
export const VoucherTabs = DepartmentVoucherTabs;

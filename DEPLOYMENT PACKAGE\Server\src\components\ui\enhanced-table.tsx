// Enhanced Table Component - Production Fix for Voucher Scrolling Issues
// Ensures all vouchers are visible and scrollable across different screen sizes

import * as React from "react"
import { cn } from "@/lib/utils"

interface EnhancedTableProps extends React.HTMLAttributes<HTMLTableElement> {
  maxHeight?: string;
  minWidth?: string;
  adaptiveHeight?: boolean;
  showAllRows?: boolean;
  bottomPadding?: string;
}

const EnhancedTable = React.forwardRef<HTMLTableElement, EnhancedTableProps>(
  ({ 
    className, 
    maxHeight, 
    minWidth = '1500px', 
    adaptiveHeight = true,
    showAllRows = true,
    bottomPadding = '4rem',
    ...props 
  }, ref) => {
    const bodyRef = React.useRef<HTMLDivElement>(null);
    const headerRef = React.useRef<HTMLDivElement>(null);
    const containerRef = React.useRef<HTMLDivElement>(null);
    
    // Calculate adaptive height based on screen size and content
    const getAdaptiveHeight = React.useCallback(() => {
      if (!adaptiveHeight) return maxHeight || '70vh';
      
      const screenHeight = window.innerHeight;
      const availableHeight = screenHeight * 0.8; // Use 80% of screen height
      
      // Minimum height for small screens
      const minHeight = Math.max(400, screenHeight * 0.4);
      
      // Maximum height for large screens
      const maxHeightPx = Math.min(availableHeight, screenHeight * 0.75);
      
      return `${Math.max(minHeight, maxHeightPx)}px`;
    }, [adaptiveHeight, maxHeight]);

    const [dynamicHeight, setDynamicHeight] = React.useState(getAdaptiveHeight());

    // Update height on window resize
    React.useEffect(() => {
      if (!adaptiveHeight) return;
      
      const handleResize = () => {
        setDynamicHeight(getAdaptiveHeight());
      };
      
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, [adaptiveHeight, getAdaptiveHeight]);

    // Synchronized scrolling between header and body
    React.useEffect(() => {
      const headerElement = headerRef.current;
      const bodyElement = bodyRef.current;

      if (!headerElement || !bodyElement) return;

      const handleHeaderScroll = () => {
        if (bodyElement && headerElement.scrollLeft !== bodyElement.scrollLeft) {
          bodyElement.scrollLeft = headerElement.scrollLeft;
        }
      };

      const handleBodyScroll = () => {
        if (headerElement && bodyElement.scrollLeft !== headerElement.scrollLeft) {
          headerElement.scrollLeft = bodyElement.scrollLeft;
        }
      };

      headerElement.addEventListener('scroll', handleHeaderScroll, { passive: true });
      bodyElement.addEventListener('scroll', handleBodyScroll, { passive: true });

      return () => {
        headerElement.removeEventListener('scroll', handleHeaderScroll);
        bodyElement.removeEventListener('scroll', handleBodyScroll);
      };
    }, []);

    // Ensure scroll to bottom works properly
    const scrollToBottom = React.useCallback(() => {
      if (bodyRef.current) {
        bodyRef.current.scrollTop = bodyRef.current.scrollHeight;
      }
    }, []);

    // Expose scroll methods
    React.useImperativeHandle(ref, () => ({
      scrollToBottom,
      scrollToTop: () => {
        if (bodyRef.current) {
          bodyRef.current.scrollTop = 0;
        }
      },
      scrollToRow: (index: number) => {
        if (bodyRef.current) {
          const rowHeight = 56; // Approximate row height
          bodyRef.current.scrollTop = index * rowHeight;
        }
      }
    } as any));

    const finalHeight = showAllRows ? 'auto' : (adaptiveHeight ? dynamicHeight : (maxHeight || '70vh'));

    return (
      <div ref={containerRef} className="flex flex-col rounded-md border w-full">
        {/* Fixed header */}
        <div className="sticky top-0 z-10 bg-background overflow-hidden border-b">
          <div 
            ref={headerRef} 
            className="overflow-x-auto scrollbar-visible" 
            style={{ scrollbarWidth: 'thin' }}
          >
            <table
              className={cn("w-full caption-bottom text-sm", className)}
              style={{ tableLayout: 'fixed', minWidth }}
            >
              {props.children && Array.isArray(props.children) ?
                props.children.filter(child =>
                  React.isValidElement(child) && child.type?.toString().includes('TableHeader')
                ) : null
              }
            </table>
          </div>
        </div>

        {/* Scrollable body with enhanced scrolling */}
        <div 
          className="overflow-auto scrollbar-visible flex-1" 
          style={{ 
            maxHeight: finalHeight,
            minHeight: showAllRows ? 'auto' : '200px',
            scrollbarWidth: 'thin', 
            overflowY: 'auto',
            overflowX: 'auto'
          }}
        >
          <div 
            ref={bodyRef} 
            className="overflow-x-auto scrollbar-visible" 
            style={{ scrollbarWidth: 'thin' }}
          >
            <table
              className={cn("w-full caption-bottom text-sm", className)}
              style={{ 
                tableLayout: 'fixed', 
                minWidth, 
                paddingBottom: bottomPadding,
                marginBottom: bottomPadding
              }}
            >
              {props.children && Array.isArray(props.children) ?
                props.children.filter(child =>
                  React.isValidElement(child) && !child.type?.toString().includes('TableHeader')
                ) : props.children
              }
            </table>
            {/* Extra padding to ensure last row is fully visible */}
            <div style={{ height: bottomPadding, minHeight: '2rem' }} />
          </div>
        </div>
      </div>
    );
  }
);

EnhancedTable.displayName = "EnhancedTable";

// Enhanced Table Header
const EnhancedTableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead ref={ref} className={cn("bg-muted/50 sticky top-0 z-10", className)} {...props} />
));
EnhancedTableHeader.displayName = "EnhancedTableHeader";

// Enhanced Table Body
const EnhancedTableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn("[&_tr:last-child]:border-0", className)}
    {...props}
  />
));
EnhancedTableBody.displayName = "EnhancedTableBody";

// Enhanced Table Row
const EnhancedTableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted h-14 min-h-[3.5rem]",
      className
    )}
    {...props}
  />
));
EnhancedTableRow.displayName = "EnhancedTableRow";

// Enhanced Table Head
const EnhancedTableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      "h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 bg-background sticky top-0 z-10",
      className
    )}
    {...props}
  />
));
EnhancedTableHead.displayName = "EnhancedTableHead";

// Enhanced Table Cell
const EnhancedTableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    className={cn("px-4 py-2 align-middle [&:has([role=checkbox])]:pr-0 min-h-[3.5rem]", className)}
    {...props}
  />
));
EnhancedTableCell.displayName = "EnhancedTableCell";

// Enhanced Table Caption
const EnhancedTableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
  <caption
    ref={ref}
    className={cn("mt-4 text-sm text-muted-foreground", className)}
    {...props}
  />
));
EnhancedTableCaption.displayName = "EnhancedTableCaption";

export {
  EnhancedTable,
  EnhancedTableHeader,
  EnhancedTableBody,
  EnhancedTableCaption,
  EnhancedTableCell,
  EnhancedTableHead,
  EnhancedTableRow,
};

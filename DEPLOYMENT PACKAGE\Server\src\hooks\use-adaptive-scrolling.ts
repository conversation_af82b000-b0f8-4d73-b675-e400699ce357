// Adaptive Scrolling Hook - Production Fix for Voucher Display Issues
// Ensures all vouchers are visible and scrollable across different screen sizes

import { useEffect, useState, useCallback, useRef } from 'react';

interface UseAdaptiveScrollingOptions {
  itemCount: number;
  itemHeight?: number;
  minHeight?: number;
  maxHeight?: number;
  containerPadding?: number;
  enableVirtualization?: boolean;
  bufferSize?: number;
}

interface ScrollingState {
  containerHeight: string;
  visibleRange: { start: number; end: number };
  scrollTop: number;
  canScrollUp: boolean;
  canScrollDown: boolean;
  totalHeight: number;
}

export function useAdaptiveScrolling({
  itemCount,
  itemHeight = 56, // Default row height
  minHeight = 300,
  maxHeight,
  containerPadding = 100,
  enableVirtualization = false,
  bufferSize = 5
}: UseAdaptiveScrollingOptions) {
  
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollState, setScrollState] = useState<ScrollingState>({
    containerHeight: 'auto',
    visibleRange: { start: 0, end: itemCount },
    scrollTop: 0,
    canScrollUp: false,
    canScrollDown: false,
    totalHeight: itemCount * itemHeight
  });

  // Calculate optimal container height
  const calculateOptimalHeight = useCallback(() => {
    const screenHeight = window.innerHeight;
    const availableHeight = screenHeight - containerPadding;
    
    // Calculate content height
    const contentHeight = itemCount * itemHeight;
    
    // Determine optimal height
    let optimalHeight: number;
    
    if (maxHeight) {
      const maxHeightPx = typeof maxHeight === 'string' 
        ? parseInt(maxHeight.replace(/\D/g, '')) 
        : maxHeight;
      optimalHeight = Math.min(contentHeight, maxHeightPx, availableHeight);
    } else {
      // Use 75% of available screen height as maximum
      const maxScreenHeight = availableHeight * 0.75;
      optimalHeight = Math.min(contentHeight, maxScreenHeight);
    }
    
    // Ensure minimum height
    optimalHeight = Math.max(optimalHeight, minHeight);
    
    // If content fits within optimal height, use auto
    if (contentHeight <= optimalHeight) {
      return 'auto';
    }
    
    return `${optimalHeight}px`;
  }, [itemCount, itemHeight, minHeight, maxHeight, containerPadding]);

  // Calculate visible range for virtualization
  const calculateVisibleRange = useCallback((scrollTop: number, containerHeight: number) => {
    if (!enableVirtualization) {
      return { start: 0, end: itemCount };
    }
    
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      itemCount,
      Math.ceil((scrollTop + containerHeight) / itemHeight)
    );
    
    // Add buffer
    const bufferedStart = Math.max(0, startIndex - bufferSize);
    const bufferedEnd = Math.min(itemCount, endIndex + bufferSize);
    
    return { start: bufferedStart, end: bufferedEnd };
  }, [itemCount, itemHeight, enableVirtualization, bufferSize]);

  // Update container height and scroll state
  const updateScrollState = useCallback(() => {
    const containerHeight = calculateOptimalHeight();
    const container = containerRef.current;
    
    if (!container) {
      setScrollState(prev => ({
        ...prev,
        containerHeight
      }));
      return;
    }
    
    const scrollTop = container.scrollTop;
    const clientHeight = container.clientHeight;
    const scrollHeight = container.scrollHeight;
    
    const visibleRange = calculateVisibleRange(scrollTop, clientHeight);
    
    setScrollState({
      containerHeight,
      visibleRange,
      scrollTop,
      canScrollUp: scrollTop > 0,
      canScrollDown: scrollTop < scrollHeight - clientHeight - 1,
      totalHeight: itemCount * itemHeight
    });
  }, [calculateOptimalHeight, calculateVisibleRange, itemCount, itemHeight]);

  // Handle scroll events
  const handleScroll = useCallback((event: Event) => {
    const container = event.target as HTMLDivElement;
    const scrollTop = container.scrollTop;
    const clientHeight = container.clientHeight;
    const scrollHeight = container.scrollHeight;
    
    const visibleRange = calculateVisibleRange(scrollTop, clientHeight);
    
    setScrollState(prev => ({
      ...prev,
      visibleRange,
      scrollTop,
      canScrollUp: scrollTop > 0,
      canScrollDown: scrollTop < scrollHeight - clientHeight - 1
    }));
  }, [calculateVisibleRange]);

  // Setup scroll listener
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    container.addEventListener('scroll', handleScroll, { passive: true });
    return () => container.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Update on item count or window resize
  useEffect(() => {
    updateScrollState();
    
    const handleResize = () => updateScrollState();
    window.addEventListener('resize', handleResize);
    
    return () => window.removeEventListener('resize', handleResize);
  }, [updateScrollState, itemCount]);

  // Scroll utilities
  const scrollToTop = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollTo({ 
        top: containerRef.current.scrollHeight, 
        behavior: 'smooth' 
      });
    }
  }, []);

  const scrollToIndex = useCallback((index: number, behavior: 'auto' | 'smooth' = 'smooth') => {
    if (containerRef.current && index >= 0 && index < itemCount) {
      const scrollTop = index * itemHeight;
      containerRef.current.scrollTo({ top: scrollTop, behavior });
    }
  }, [itemCount, itemHeight]);

  const scrollIntoView = useCallback((index: number) => {
    if (!containerRef.current || index < 0 || index >= itemCount) return;
    
    const container = containerRef.current;
    const itemTop = index * itemHeight;
    const itemBottom = itemTop + itemHeight;
    const containerTop = container.scrollTop;
    const containerBottom = containerTop + container.clientHeight;
    
    if (itemTop < containerTop) {
      // Item is above visible area
      container.scrollTo({ top: itemTop, behavior: 'smooth' });
    } else if (itemBottom > containerBottom) {
      // Item is below visible area
      container.scrollTo({ 
        top: itemBottom - container.clientHeight, 
        behavior: 'smooth' 
      });
    }
  }, [itemCount, itemHeight]);

  // Get container styles
  const getContainerStyles = useCallback(() => ({
    height: scrollState.containerHeight,
    minHeight: `${minHeight}px`,
    overflowY: 'auto' as const,
    overflowX: 'auto' as const,
    scrollbarWidth: 'thin' as const,
    position: 'relative' as const
  }), [scrollState.containerHeight, minHeight]);

  // Get virtualized items (if virtualization enabled)
  const getVirtualizedItems = useCallback(<T,>(items: T[]) => {
    if (!enableVirtualization) return items;
    
    const { start, end } = scrollState.visibleRange;
    return items.slice(start, end);
  }, [enableVirtualization, scrollState.visibleRange]);

  // Get spacer heights for virtualization
  const getSpacerHeights = useCallback(() => {
    if (!enableVirtualization) return { top: 0, bottom: 0 };
    
    const { start, end } = scrollState.visibleRange;
    return {
      top: start * itemHeight,
      bottom: (itemCount - end) * itemHeight
    };
  }, [enableVirtualization, scrollState.visibleRange, itemHeight, itemCount]);

  return {
    containerRef,
    scrollState,
    scrollToTop,
    scrollToBottom,
    scrollToIndex,
    scrollIntoView,
    getContainerStyles,
    getVirtualizedItems,
    getSpacerHeights,
    // Utility functions
    isItemVisible: (index: number) => {
      const { start, end } = scrollState.visibleRange;
      return index >= start && index < end;
    },
    getScrollPercentage: () => {
      if (!containerRef.current) return 0;
      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      return scrollHeight > clientHeight 
        ? (scrollTop / (scrollHeight - clientHeight)) * 100 
        : 0;
    }
  };
}

// Specialized hook for voucher tables
export function useVoucherScrolling(voucherCount: number) {
  return useAdaptiveScrolling({
    itemCount: voucherCount,
    itemHeight: 56, // Standard voucher row height
    minHeight: 300,
    containerPadding: 150, // Account for headers, filters, etc.
    enableVirtualization: voucherCount > 100, // Enable for large lists
    bufferSize: 10
  });
}

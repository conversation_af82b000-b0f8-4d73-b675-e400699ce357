
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UnifiedVoucherBadges } from '@/components/common/UnifiedVoucherBadges';
import { formatNumberWithCommas, formatVMSDateTime } from '@/utils/formatUtils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import React, { useRef, useEffect, useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Voucher } from '@/lib/types';
import { Eye, Trash2, RefreshCw } from 'lucide-react';
import { SortableColumnHeader } from './sortable-column-header';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogHeader } from '@/components/ui/dialog';
import { useAppStore } from '@/lib/store';
import { toast } from 'sonner';

interface RejectedVouchersTabProps {
  filteredVouchers: Voucher[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  onViewVoucher?: (voucher: Voucher) => void;
  onDeleteVoucher: (voucherId: string) => void;
  onAddBackToSubmission?: (voucher: Voucher) => void;
  isAudit?: boolean;
}

export function RejectedVouchersTab({
  filteredVouchers,
  sortColumn,
  sortDirection,
  handleSort,
  onViewVoucher,
  onDeleteVoucher,
  onAddBackToSubmission,
  isAudit = false
}: RejectedVouchersTabProps) {
  const updateVoucher = useAppStore(state => state.updateVoucher);
  const [selectedVoucher, setSelectedVoucher] = useState<Voucher | null>(null);

  // Refs for synchronized scrolling
  const headerRef = useRef<HTMLDivElement>(null);
  const bodyRef = useRef<HTMLDivElement>(null);

  // Set up synchronized scrolling between header and body
  useEffect(() => {
    const headerElement = headerRef.current;
    const bodyElement = bodyRef.current;

    if (!headerElement || !bodyElement) return;

    const handleHeaderScroll = () => {
      if (bodyElement) {
        bodyElement.scrollLeft = headerElement.scrollLeft;
      }
    };

    const handleBodyScroll = () => {
      if (headerElement) {
        headerElement.scrollLeft = bodyElement.scrollLeft;
      }
    };

    headerElement.addEventListener('scroll', handleHeaderScroll);
    bodyElement.addEventListener('scroll', handleBodyScroll);

    return () => {
      headerElement.removeEventListener('scroll', handleHeaderScroll);
      bodyElement.removeEventListener('scroll', handleBodyScroll);
    };
  }, []);

  // Debug logging for rejected vouchers (only log once when vouchers change)
  useEffect(() => {
    console.log("RejectedVouchersTab: Displaying vouchers:", filteredVouchers.length);
    filteredVouchers.forEach(v => {
      console.log(`Rejected voucher in tab: ${v.voucherId}, Status: ${v.status}, Deleted: ${v.deleted}, ID: ${v.id}`);
    });
  }, [filteredVouchers.length]);

  // Function to format currency values
  const formatCurrency = (amount: number, currency: string) => {
    return `${formatNumberWithCommas(amount)} ${currency}`;
  };

  // Function to view voucher details
  const handleViewVoucherDetails = (voucher: Voucher) => {
    if (onViewVoucher) {
      onViewVoucher(voucher);
    } else {
      setSelectedVoucher(voucher);
    }
  };

  const handleCloseDialog = () => {
    setSelectedVoucher(null);
  };

  // Function to add back to pending
  const handleAddBack = (voucher: Voucher) => {
    // We only allow departments to add rejected vouchers back to pending
    if (isAudit) return;

    try {
      console.log(`Adding voucher ${voucher.id} (${voucher.voucherId}) back to pending submission`);

      // APPROACH 1: Preserve the original voucher ID when adding rejected vouchers back to PENDING
      // This maintains traceability and history of the voucher throughout its lifecycle
      // The voucher ID remains in the blacklist for record-keeping, but this doesn't prevent resubmission

      // RESUBMISSION WORKFLOW FIX: Follow normal workflow path with resubmission flag
      const currentDate = new Date().toLocaleDateString();

      // CRITICAL FIX: Preserve original rejection reason before updating
      const originalRejectionReason = voucher.comment || voucher.original_rejection_reason || voucher.originalRejectionReason;
      const originalRejectedBy = voucher.rejectedBy || voucher.rejected_by || voucher.originalRejectedBy;

      updateVoucher(voucher.id, {
        status: "PENDING SUBMISSION", // Normal pending status
        workflow_state: "FINANCE_PENDING", // Normal workflow state
        sentToAudit: false,
        deleted: false,
        // RESUBMISSION FLAG: Mark as resubmitted for badge display
        isResubmitted: true,
        is_resubmitted: 1, // Server-side flag
        lastResubmissionDate: new Date().toISOString(),
        // Reset dispatch fields but preserve rejection history for traceability
        auditDispatchTime: undefined,
        auditDispatchedBy: undefined,
        dispatched: false,
        dispatchTime: undefined,
        dispatchedBy: undefined,
        batch_id: null, // Clear batch association
        // CRITICAL FIX: Preserve original rejection reason in dedicated fields
        original_rejection_reason: originalRejectionReason,
        originalRejectionReason: originalRejectionReason, // camelCase for frontend
        original_rejected_by: originalRejectedBy,
        originalRejectedBy: originalRejectedBy, // camelCase for frontend
        // PRESERVE REJECTION HISTORY: Keep for audit trail and badge logic
        // rejected_by: preserved
        // rejection_time: preserved
        // comment: preserved (original rejection reason)
      });

      toast.success(`Voucher ${voucher.voucherId} re-added from rejection to pending submissions`, {
        duration: 3000
      });

      if (onAddBackToSubmission) {
        onAddBackToSubmission(voucher);
      }
    } catch (error) {
      toast.error('Failed to add voucher back to pending', {
        duration: 3000
      });
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex flex-col rounded-md border">
        {/* Fixed header */}
        <div className="sticky top-0 z-10 bg-background overflow-hidden">
          <div ref={headerRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
            <thead>
              <tr className="bg-background">
                <SortableColumnHeader
                  title="VOUCHER ID"
                  sortKey="voucherId"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <SortableColumnHeader
                  title="DATE"
                  sortKey="date"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <SortableColumnHeader
                  title="CLAIMANT"
                  sortKey="claimant"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <SortableColumnHeader
                  title="DESCRIPTION"
                  sortKey="description"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <SortableColumnHeader
                  title="AMOUNT"
                  sortKey="amount"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10">REJECTION TIME</TableHead>
                {isAudit && (
                  <>
                    <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10">CREATED BY</TableHead>
                    <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10">DISPATCHED BY</TableHead>
                    <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10">REJECTED BY</TableHead>
                  </>
                )}
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10">ACTIONS</TableHead>
              </tr>
            </thead>
            </table>
          </div>
        </div>

        {/* PRODUCTION FIX: Enhanced scrollable body with adaptive height */}
        <div
          className="overflow-auto scrollbar-visible"
          style={{
            height: `${Math.max(400, Math.min(window.innerHeight * 0.7, filteredVouchers.length * 56 + 100))}px`,
            minHeight: '400px',
            maxHeight: '80vh',
            scrollbarWidth: 'thin',
            overflowY: 'auto',
            overflowX: 'auto'
          }}
        >
          <div ref={bodyRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px', paddingBottom: '3rem' }}>
              <tbody>
              {filteredVouchers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={isAudit ? 9 : 7} className="h-24 text-center uppercase">
                    NO REJECTED VOUCHERS FOUND.
                  </TableCell>
                </TableRow>
              ) : (
                filteredVouchers.map((voucher) => (
                  <TableRow
                    key={voucher.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={(e) => {
                      // Don't trigger row click when clicking on action buttons
                      if ((e.target as HTMLElement).closest('button')) return;
                      handleViewVoucherDetails(voucher);
                    }}
                  >
                    <TableCell className="font-medium uppercase whitespace-nowrap">
                      <div className="flex flex-col items-center gap-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="block truncate">{voucher.voucherId}</span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{voucher.voucherId}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        {/* PRODUCTION FIX: Missing badges in rejected vouchers tab */}
                        <UnifiedVoucherBadges
                          voucher={voucher}
                          tabName="rejected"
                          size="sm"
                          className="flex-wrap justify-center"
                        />
                      </div>
                    </TableCell>
                    <TableCell className="uppercase whitespace-nowrap">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.date}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.date}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="uppercase">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.claimant}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.claimant}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="max-w-[200px] truncate uppercase">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.description}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="uppercase whitespace-nowrap">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{formatCurrency(voucher.amount, voucher.currency)}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{formatCurrency(voucher.amount, voucher.currency)}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="uppercase whitespace-nowrap">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.rejectionTime ? formatVMSDateTime(voucher.rejectionTime) : "N/A"}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.rejectionTime ? formatVMSDateTime(voucher.rejectionTime) : "N/A"}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    {isAudit && (
                      <>
                        <TableCell className="uppercase whitespace-nowrap">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{voucher.createdBy || "N/A"}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{voucher.createdBy || "N/A"}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase whitespace-nowrap">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">
                                  {voucher.auditDispatchedBy || voucher.dispatchedBy || "N/A"}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{voucher.auditDispatchedBy || voucher.dispatchedBy || "N/A"}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase whitespace-nowrap">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{voucher.rejectedBy || "N/A"}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{voucher.rejectedBy || "N/A"}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                      </>
                    )}
                    <TableCell className="whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {onViewVoucher && (
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => onViewVoucher(voucher)}
                            title="View Voucher Details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}

                        {/* Only show Add Back button if not Audit department */}
                        {!isAudit && onAddBackToSubmission && (
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => handleAddBack(voucher)}
                            title="Add Back to Submission"
                            className="text-blue-500 hover:text-blue-700 hover:bg-blue-100"
                          >
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                        )}

                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => onDeleteVoucher(voucher.id)}
                          title="Delete Voucher"
                          className="text-red-500 hover:text-red-700 hover:bg-red-100"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Voucher Details Dialog */}
      {selectedVoucher && (
        <Dialog open={!!selectedVoucher} onOpenChange={() => handleCloseDialog()}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="text-center">VOUCHER DETAILS</DialogTitle>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold">Voucher ID:</p>
                  <p>{selectedVoucher.voucherId}</p>
                </div>
                <div>
                  <p className="font-semibold">Date:</p>
                  <p>{selectedVoucher.dateReceived}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold">Claimant:</p>
                  <p>{selectedVoucher.claimant}</p>
                </div>
                <div>
                  <p className="font-semibold">Status:</p>
                  <p>{selectedVoucher.status}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold">Description:</p>
                  <p>{selectedVoucher.description}</p>
                </div>
                <div>
                  <p className="font-semibold">Pre-Audited Amount:</p>
                  <p>{formatCurrency(selectedVoucher.preAuditedAmount || selectedVoucher.amount, selectedVoucher.currency)}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold">Amount:</p>
                  <p>{formatCurrency(selectedVoucher.amount, selectedVoucher.currency)}</p>
                </div>
                <div>
                  <p className="font-semibold">Department:</p>
                  <p>{selectedVoucher.department}</p>
                </div>
              </div>

              {selectedVoucher.rejectionReason && (
                <div>
                  <p className="font-semibold">Rejection Reason:</p>
                  <p>{selectedVoucher.rejectionReason}</p>
                </div>
              )}

              {selectedVoucher.comment && (
                <div>
                  <p className="font-semibold">Comment:</p>
                  <p>{selectedVoucher.comment}</p>
                </div>
              )}

              {/* Enhanced tracking information for rejected vouchers */}
              {selectedVoucher.isRejectedVoucher && (
                <div className="border-t pt-4 mt-4">
                  <h4 className="font-semibold text-lg mb-3">Rejection Tracking Details</h4>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="font-semibold">Rejected By:</p>
                      <p>{selectedVoucher.rejectedBy || "N/A"}</p>
                    </div>
                    <div>
                      <p className="font-semibold">Rejection Time:</p>
                      <p>{selectedVoucher.rejectionTime || "N/A"}</p>
                    </div>
                  </div>

                  {(selectedVoucher.auditDispatchedBy || selectedVoucher.dispatchedBy || selectedVoucher.rejectedDispatchedBy) && (
                    <div className="grid grid-cols-2 gap-4 mt-3">
                      <div>
                        <p className="font-semibold">Dispatched By:</p>
                        <p>{selectedVoucher.auditDispatchedBy || selectedVoucher.dispatchedBy || selectedVoucher.rejectedDispatchedBy}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Dispatch Time:</p>
                        <p>{selectedVoucher.auditDispatchTime || selectedVoucher.dispatchTime || selectedVoucher.rejectedDispatchTime || "N/A"}</p>
                      </div>
                    </div>
                  )}

                  {selectedVoucher.rejectedReceivedBy && (
                    <div className="grid grid-cols-2 gap-4 mt-3">
                      <div>
                        <p className="font-semibold">Received By (Department):</p>
                        <p>{selectedVoucher.rejectedReceivedBy}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Receipt Time:</p>
                        <p>{selectedVoucher.rejectedReceiptTime || "N/A"}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}


import { toast as sonnerToast } from "sonner";
import * as React from "react";

type ToastProps = {
  title?: string;
  description?: string;
  variant?: "default" | "destructive" | "success";
  duration?: number;
};

const TOAST_LIMIT = 20;
const TOAST_REMOVE_DELAY = 1000;

type ToasterToastActionElement = React.ReactElement;

export type ToasterToast = {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToasterToastActionElement;
  variant?: "default" | "destructive" | "success";
};

const toastStore = {
  toasts: [] as ToasterToast[],
  listeners: new Set<() => void>(),
};

const createToast = (props: ToastProps): ToasterToast => {
  const id = Math.random().toString(36).substring(2, 9);
  const toast: ToasterToast = {
    id,
    title: props.title,
    description: props.description,
    variant: props.variant,
  };
  
  return toast;
};

const addToast = (toast: ToasterToast) => {
  toastStore.toasts = [...toastStore.toasts, toast].slice(-TOAST_LIMIT);
  toastStore.listeners.forEach((listener) => listener());

  return toast.id;
};

const dismissToast = (toastId: string) => {
  toastStore.toasts = toastStore.toasts.filter((t) => t.id !== toastId);
  toastStore.listeners.forEach((listener) => listener());
};

export const useToast = () => {
  const [toasts, setToasts] = React.useState<ToasterToast[]>(toastStore.toasts);

  React.useEffect(() => {
    const listener = () => {
      setToasts([...toastStore.toasts]);
    };

    toastStore.listeners.add(listener);
    return () => {
      toastStore.listeners.delete(listener);
    };
  }, []);

  return {
    toasts,
    toast: (props: ToastProps) => {
      const toast = createToast(props);
      addToast(toast);
      return toast.id;
    },
    dismiss: (toastId: string) => dismissToast(toastId),
  };
};

export const toast = ({ 
  title, 
  description, 
  variant = "default", 
  duration = 5000 
}: ToastProps) => {
  const options = {
    duration,
  };

  if (variant === "destructive") {
    return sonnerToast.error(title, {
      description,
      ...options,
    });
  }

  if (variant === "success") {
    return sonnerToast.success(title, {
      description,
      ...options,
    });
  }

  return sonnerToast(title, {
    description,
    ...options,
  });
};

import { useEffect, useState } from 'react';
import { Users } from 'lucide-react';
import { Department } from '@/lib/types';
import { Badge } from '@/components/ui/badge';

// Simple in-memory active users tracking
const activeUsers: Record<Department, string[]> = {
  'FINANCE': [],
  'AUDIT': [],
  'MINISTRIES': [],
  'PENSIONS': [],
  'PENTMEDIA': [],
  'MISSIONS': [],
  'PENTSOS': [],
  'SYSTEM ADMIN': []
};

// Maximum concurrent users per department
const MAX_CONCURRENT_USERS: Record<Department, number> = {
  'FINANCE': 4,
  'AUDIT': 6,
  'MINISTRIES': 4,
  'PENSIONS': 4,
  'PENTMEDIA': 4,
  'MISSIONS': 4,
  'PENTSOS': 4,
  'SYSTEM ADMIN': 1
};

interface DepartmentUserCountProps {
  department: Department;
}

export function DepartmentUserCount({ department }: DepartmentUserCountProps) {
  const [userCount, setUserCount] = useState(0);
  const [maxUsers, setMaxUsers] = useState(4);
  
  useEffect(() => {
    // Generate a unique user ID if not already present
    if (!sessionStorage.getItem('userId')) {
      sessionStorage.setItem('userId', `user-${Date.now()}`);
    }
    
    // Add this user to the active users for this department
    const userId = sessionStorage.getItem('userId')!;
    if (!activeUsers[department].includes(userId)) {
      activeUsers[department].push(userId);
    }
    
    // Set the user count and max users
    setUserCount(activeUsers[department].length);
    setMaxUsers(MAX_CONCURRENT_USERS[department] || 4);
    
    // Clean up when component unmounts
    return () => {
      const index = activeUsers[department].indexOf(userId);
      if (index !== -1) {
        activeUsers[department].splice(index, 1);
      }
    };
  }, [department]);
  
  return (
    <Badge variant="outline" className="flex items-center gap-1 py-1">
      <Users className="h-3.5 w-3.5" />
      <span>{userCount}/{maxUsers} Users</span>
    </Badge>
  );
}

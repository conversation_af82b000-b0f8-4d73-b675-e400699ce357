/**
 * Department Voucher Tabs Hook - Production Ready
 * 
 * Simple, focused hook for Department Voucher Hubs that:
 * - Uses existing store methods (no workflow complexity)
 * - Manages basic activeTab state
 * - Provides department-specific voucher filtering
 * - Clean separation from workflow system
 */

import { useState, useMemo, useEffect } from 'react';
import { useAppStore } from '@/lib/store';
import { Department, Voucher } from '@/lib/types';

export interface DepartmentVoucherTabsResult {
  // Tab state management
  activeTab: string;
  setActiveTab: (tab: string) => void;
  
  // Department-specific voucher lists
  newVouchers: Voucher[];
  pendingDispatchVouchers: Voucher[];
  dispatchedVouchers: Voucher[];
  returnedVouchers: Voucher[];
  rejectedVouchers: Voucher[];
}

export function useDepartmentVoucherTabs(department: Department): DepartmentVoucherTabsResult {
  // Simple tab state management - starts with 'new-vouchers'
  const [activeTab, setActiveTab] = useState<string>('new-vouchers');
  const [forceUpdate, setForceUpdate] = useState(0);

  // Get vouchers from store using existing methods
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);

  // CRITICAL FIX: Fetch vouchers when component mounts
  useEffect(() => {
    console.log(`🔄 Department Voucher Hub (${department}): Component mounted, fetching vouchers`);
    console.log(`🔄 Department Voucher Hub (${department}): Current voucher count in store:`, vouchers.length);

    // Fetch ALL vouchers so we can filter by originalDepartment
    fetchVouchers('ALL').then(() => {
      console.log(`✅ Department Voucher Hub (${department}): Vouchers fetched successfully`);
      const updatedVouchers = useAppStore.getState().vouchers;
      console.log(`✅ Department Voucher Hub (${department}): Updated voucher count:`, updatedVouchers.length);

      // Debug: Show vouchers that match this department
      const matchingVouchers = updatedVouchers.filter(v => v.originalDepartment === department);
      console.log(`🔍 Department Voucher Hub (${department}): Found ${matchingVouchers.length} vouchers with originalDepartment = ${department}`);
      matchingVouchers.forEach(v => {
        console.log(`  - ${v.voucherId}: dept=${v.department}, orig=${v.originalDepartment}, status=${v.status}`);
      });
    }).catch((error) => {
      console.error(`❌ Department Voucher Hub (${department}): Error fetching vouchers:`, error);
    });
  }, [department, fetchVouchers]);

  // CRITICAL FIX: Listen for ALL voucher events to force immediate tab and status updates
  useEffect(() => {
    const handleVoucherUpdate = (event: CustomEvent) => {
      const eventType = event.detail?.type;
      console.log(`🔄 REAL-TIME STATUS: Department voucher tabs received update event: ${eventType}`);

      // Force update for any voucher change to ensure status columns update
      if (eventType === 'dispatch_confirmed' ||
          eventType === 'updated' ||
          eventType === 'workflow_transition' ||
          event.detail?.forceTabRefresh) {
        console.log(`🔄 REAL-TIME STATUS: Force updating department voucher tabs for ${department}`);
        setForceUpdate(prev => prev + 1);
      }
    };

    window.addEventListener('voucherUpdated', handleVoucherUpdate as EventListener);
    return () => {
      window.removeEventListener('voucherUpdated', handleVoucherUpdate as EventListener);
    };
  }, [department]);

  // Listen for voucher updates to refresh data without page reload
  useEffect(() => {
    const handleVoucherUpdate = (event: CustomEvent) => {
      console.log(`🔄 Department Voucher Hub (${department}): Voucher updated, triggering immediate refresh`);

      // REAL-TIME TAB MOVEMENT FIX: Immediate refresh without delay
      const performImmediateRefresh = async () => {
        try {
          console.log(`🔄 IMMEDIATE REFRESH: Triggering force update for ${department}`);

          // Force immediate re-render by updating the force update counter
          setForceUpdate(prev => {
            const newValue = prev + 1;
            console.log(`🔄 FORCE UPDATE: ${department} counter: ${prev} -> ${newValue}`);
            return newValue;
          });

          // Also trigger a second update after a tiny delay to ensure re-render
          setTimeout(() => {
            setForceUpdate(prev => {
              const newValue = prev + 1;
              console.log(`🔄 DOUBLE UPDATE: ${department} counter: ${prev} -> ${newValue}`);
              return newValue;
            });
          }, 5);

          // Background fetch for data consistency (don't wait for it)
          setTimeout(async () => {
            try {
              console.log(`🔄 BACKGROUND FETCH: Starting for ${department}`);
              await fetchVouchers('ALL');
              console.log(`✅ BACKGROUND FETCH: Completed for ${department}`);
            } catch (error) {
              console.error(`❌ BACKGROUND FETCH: Error for ${department}:`, error);
            }
          }, 50);
        } catch (error) {
          console.error(`❌ IMMEDIATE REFRESH: Error for ${department}:`, error);
        }
      };

      performImmediateRefresh();
    };

    window.addEventListener('voucherUpdated', handleVoucherUpdate as EventListener);

    // RETURNED VOUCHER BACKSTAGE REFRESH: Listen specifically for returned voucher events
    const handleReturnedVoucherUpdate = (event: CustomEvent) => {
      const { detail } = event;
      console.log(`🔄 RETURNED VOUCHER BACKSTAGE REFRESH: Detected returned voucher event for ${department}`, detail);

      // Immediate backstage refresh for returned vouchers
      const performImmediateBackstageRefresh = async () => {
        try {
          console.log(`🔄 IMMEDIATE BACKSTAGE REFRESH: Starting for returned voucher in ${department}`);

          // Longer delay for returned vouchers to ensure all database operations complete
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Force complete data refresh
          await fetchVouchers('ALL');

          console.log(`✅ IMMEDIATE BACKSTAGE REFRESH: Completed for returned voucher in ${department}`);
        } catch (error) {
          console.error(`❌ IMMEDIATE BACKSTAGE REFRESH: Error for returned voucher in ${department}:`, error);
        }
      };

      performImmediateBackstageRefresh();
    };

    window.addEventListener('voucherUpdated', handleVoucherUpdate as EventListener);
    window.addEventListener('returnedVoucherCreated', handleReturnedVoucherUpdate as EventListener);

    return () => {
      window.removeEventListener('voucherUpdated', handleVoucherUpdate as EventListener);
      window.removeEventListener('returnedVoucherCreated', handleReturnedVoucherUpdate as EventListener);
    };
  }, [department, fetchVouchers]);

  // PERIODIC BACKSTAGE REFRESH: For Return tab to ensure returned vouchers appear
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    // Only run periodic refresh for Finance department (where Return tab exists)
    if (department === 'FINANCE') {
      intervalId = setInterval(async () => {
        try {
          await fetchVouchers('ALL');
        } catch (error) {
          console.error(`Periodic refresh error for ${department}:`, error);
        }
      }, 120000); // Refresh every 2 minutes (reduced from 30s for performance)
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
        console.log(`🔄 PERIODIC BACKSTAGE REFRESH: Stopped for ${department}`);
      }
    };
  }, [department, fetchVouchers]);

  // Filter vouchers by department and status using existing logic
  const departmentVouchers = useMemo(() => {
    return vouchers.filter(voucher => {
      // CRITICAL FIX: Department Voucher Hubs show vouchers that ORIGINATED from that department
      // These vouchers are currently in AUDIT but originally came from the specified department
      if (voucher.originalDepartment !== department) return false;

      // Include vouchers that are currently being processed by Audit for this department
      // These are vouchers that originated from this department and are now in Audit workflow
      return true;
    });
  }, [vouchers, department]);
  
  // Categorize vouchers into department tabs using existing business logic
  const categorizedVouchers = useMemo(() => {
    const newVouchers: Voucher[] = [];
    const pendingDispatchVouchers: Voucher[] = [];
    const dispatchedVouchers: Voucher[] = [];
    const returnedVouchers: Voucher[] = [];
    const rejectedVouchers: Voucher[] = [];
    
    departmentVouchers.forEach(voucher => {
      // NEW VOUCHERS: Vouchers from this department that are new to Audit (ready to work on)
      // HOLD FEATURE FIX: Keep held vouchers in NEW VOUCHER tab regardless of workStarted status
      if (voucher.originalDepartment === department &&
          voucher.department === 'AUDIT' &&
          voucher.status === 'AUDIT: PROCESSING' &&
          voucher.sentToAudit === true &&
          (voucher.workStarted !== true || voucher.is_on_hold === true)) {
        newVouchers.push(voucher);
      }
      // PENDING DISPATCH: Vouchers processed by Audit and ready to send back to department
      // HOLD FEATURE FIX: Exclude held vouchers from PENDING DISPATCH (they stay in NEW VOUCHER)
      else if (voucher.originalDepartment === department &&
               voucher.department === 'AUDIT' &&
               voucher.status === 'AUDIT: PROCESSING' &&
               voucher.sentToAudit === true &&
               voucher.workStarted === true &&
               voucher.is_on_hold !== true &&
               !voucher.auditDispatchedBy) {
        pendingDispatchVouchers.push(voucher);
      }
      // CRITICAL FIX: PENDING DISPATCH - Include returned vouchers ready for dispatch back to Finance
      else if (voucher.originalDepartment === department &&
               voucher.department === 'AUDIT' &&
               voucher.status === 'VOUCHER RETURNED' &&
               voucher.workflow_state === 'AUDIT_PENDING_DISPATCH_RETURNED' &&
               !voucher.auditDispatchedBy) {
        pendingDispatchVouchers.push(voucher);
      }
      // DISPATCHED: Vouchers sent back to department (but not rejected)
      else if (voucher.originalDepartment === department &&
               voucher.auditDispatchedBy &&
               voucher.status !== 'VOUCHER REJECTED' &&
               voucher.status !== 'VOUCHER RETURNED') {
        dispatchedVouchers.push(voucher);
      }

      // RETURNED: Return copies that originated from this department and are now in Audit
      else if (voucher.originalDepartment === department &&
               voucher.department === 'AUDIT' &&
               (voucher.is_returned_copy === true || voucher.isReturnedCopy === true) &&
               voucher.status === 'VOUCHER RETURNED' &&
               voucher.workflow_state === 'AUDIT_RETURNED_COPY') {
        returnedVouchers.push(voucher);
      }

      // REJECTED: Only COPY vouchers rejected by Audit (permanent records)
      else if (voucher.originalDepartment === department &&
               voucher.status === 'VOUCHER REJECTED' &&
               voucher.isRejectionCopy === true) {
        rejectedVouchers.push(voucher);
      }
      // PENDING DISPATCH: Original rejected vouchers ready to be dispatched back
      else if (voucher.originalDepartment === department &&
               voucher.status === 'VOUCHER REJECTED' &&
               voucher.isRejectionCopy !== true &&
               !voucher.auditDispatchedBy) {
        pendingDispatchVouchers.push(voucher);
      }
    });
    
    return {
      newVouchers,
      pendingDispatchVouchers,
      dispatchedVouchers,
      returnedVouchers,
      rejectedVouchers
    };
  }, [departmentVouchers, forceUpdate]);
  
  return {
    activeTab,
    setActiveTab,
    ...categorizedVouchers
  };
}

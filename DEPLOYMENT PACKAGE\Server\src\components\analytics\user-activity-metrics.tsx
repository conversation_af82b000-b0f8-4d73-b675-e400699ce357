import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { formatNumberWithCommas } from '@/utils/formatUtils';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts';

interface UserMetric {
  name: string;
  vouchersProcessed: number;
  avgProcessingTime: number;
  certificationRate: number;
  rejectionRate: number;
  preAuditSavings: number;
  preAuditSavingsPercentage: number;
}

interface ActivityHeatmapData {
  hour: number;
  day: string;
  value: number;
}

interface MonthlyTopPerformer {
  month: string;
  preAuditTopPerformer: string;
  preAuditSavings: number;
  certificationTopPerformer: string;
  certificationRate: number;
}

interface UserActivityData {
  userMetrics: UserMetric[];
  activityHeatmap: ActivityHeatmapData[];
  topPerformer: string;
  bottomPerformer: string;
  monthlyTopPerformers: MonthlyTopPerformer[];
  yearlyPreAuditTopPerformer: string;
  yearlyPreAuditSavings: number;
  yearlyCertificationTopPerformer: string;
  yearlyCertificationRate: number;
}

interface UserActivityMetricsProps {
  data: UserActivityData;
}

export function UserActivityMetrics({ data }: UserActivityMetricsProps) {
  const [metric, setMetric] = useState<'vouchersProcessed' | 'avgProcessingTime' | 'certificationRate' | 'preAuditSavings'>('vouchersProcessed');
  const [view, setView] = useState<'metrics' | 'topPerformers'>('metrics');

  const metricConfig = {
    vouchersProcessed: {
      label: 'Vouchers Processed',
      valueFormatter: (value: number) => value.toString(),
      color: '#6366f1'
    },
    avgProcessingTime: {
      label: 'Avg. Processing Time (hrs)',
      valueFormatter: (value: number) => `${value} hrs`,
      color: '#f59e0b'
    },
    certificationRate: {
      label: 'Certification Rate (%)',
      valueFormatter: (value: number) => `${value}%`,
      color: '#10b981'
    },
    preAuditSavings: {
      label: 'Pre-audit Savings (GHS)',
      valueFormatter: (value: number) => `GHS ${formatNumberWithCommas(value)}`,
      color: '#22c55e'
    }
  };

  const currentMetric = metricConfig[metric];

  // Find the max value for the current metric to calculate relative percentages
  const maxValue = Math.max(...data.userMetrics.map(user => user[metric]));

  // Sort users by the current metric (descending for vouchers and certification, ascending for time)
  const sortedUsers = [...data.userMetrics].sort((a, b) => {
    if (metric === 'avgProcessingTime') {
      return a[metric] - b[metric]; // Lower is better for processing time
    }
    return b[metric] - a[metric]; // Higher is better for other metrics
  });

  const renderUserMetrics = () => (
    <div className="space-y-4">
      <div className="space-y-4">
        {sortedUsers.map((user) => (
          <div key={user.name} className="space-y-1">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">{user.name}</span>
              <span className="text-sm text-muted-foreground">
                {currentMetric.valueFormatter(user[metric])}
              </span>
            </div>
            <Progress
              value={(user[metric] / maxValue) * 100}
              className="h-2"
              indicatorClassName={`bg-${metric === 'avgProcessingTime' ? 'amber-500' : (metric === 'certificationRate' ? 'green-500' : 'blue-500')}`}
            />
          </div>
        ))}
      </div>

      <div className="pt-4 border-t">
        <h4 className="text-sm font-medium mb-2">Activity Heatmap</h4>
        <div className="h-[200px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data.activityHeatmap}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              layout="vertical"
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="day" type="category" width={80} />
              <Tooltip
                formatter={(value: number) => [`${value} vouchers`, 'Activity']}
                labelFormatter={(label) => `${label}`}
              />
              <Bar dataKey="value" name="Vouchers Processed" fill="#6366f1" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );

  const renderTopPerformers = () => (
    <div className="space-y-6">
      {/* Yearly Top Performers */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Yearly Top Performers</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="p-4 border rounded-md">
            <div className="text-sm text-muted-foreground">Pre-audit Savings Champion</div>
            <div className="text-xl font-bold">{data.yearlyPreAuditTopPerformer}</div>
            <div className="text-sm text-green-600">
              GHS {formatNumberWithCommas(data.yearlyPreAuditSavings)} saved
            </div>
          </div>
          <div className="p-4 border rounded-md">
            <div className="text-sm text-muted-foreground">Certification Champion</div>
            <div className="text-xl font-bold">{data.yearlyCertificationTopPerformer}</div>
            <div className="text-sm text-blue-600">
              {data.yearlyCertificationRate}% certification rate
            </div>
          </div>
        </div>
      </div>

      {/* Monthly Top Performers */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Monthly Top Performers</h3>
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Month</TableHead>
                <TableHead>Pre-audit Champion</TableHead>
                <TableHead className="text-right">Savings</TableHead>
                <TableHead>Certification Champion</TableHead>
                <TableHead className="text-right">Rate</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.monthlyTopPerformers.map((performer) => (
                <TableRow key={performer.month}>
                  <TableCell className="font-medium">{performer.month}</TableCell>
                  <TableCell>{performer.preAuditTopPerformer}</TableCell>
                  <TableCell className="text-right text-green-600">
                    GHS {formatNumberWithCommas(performer.preAuditSavings)}
                  </TableCell>
                  <TableCell>{performer.certificationTopPerformer}</TableCell>
                  <TableCell className="text-right text-blue-600">
                    {performer.certificationRate}%
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Tabs value={metric} onValueChange={(v) => setMetric(v as any)} className="w-full max-w-md">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="vouchersProcessed">Volume</TabsTrigger>
            <TabsTrigger value="avgProcessingTime">Time</TabsTrigger>
            <TabsTrigger value="certificationRate">Certification</TabsTrigger>
            <TabsTrigger value="preAuditSavings">Pre-audit</TabsTrigger>
          </TabsList>
        </Tabs>

        <Tabs value={view} onValueChange={(v) => setView(v as any)} className="w-full max-w-md">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="metrics">User Metrics</TabsTrigger>
            <TabsTrigger value="topPerformers">Top Performers</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {view === 'metrics' ? renderUserMetrics() : renderTopPerformers()}

      <div className="pt-2 text-sm text-muted-foreground">
        <div className="flex justify-between">
          <span>Overall top performer: <strong>{data.topPerformer}</strong></span>
          <span>Most active time: <strong>Tuesdays, 10-11 AM</strong></span>
        </div>
      </div>
    </div>
  );
}

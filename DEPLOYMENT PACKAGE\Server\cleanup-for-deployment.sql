-- ============================================================
-- VMS PRODUCTION DATABASE CLEANUP FOR DEPLOYMENT
-- ============================================================
-- This script cleans the database for production deployment
-- Removes all vouchers, batches, and user data except admin accounts
-- Keeps only essential admin users and system settings
-- ============================================================

USE vms_production;

-- ============================================================
-- STEP 1: DISABLE FOREIGN KEY CHECKS FOR SAFE CLEANUP
-- ============================================================
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================
-- STEP 2: CLEAN ALL VOUCHER-RELATED DATA
-- ============================================================

-- Remove all provisional cash records
DELETE FROM provisional_cash_records;
ALTER TABLE provisional_cash_records AUTO_INCREMENT = 1;

-- Remove all batch-voucher relationships
DELETE FROM batch_vouchers;

-- Remove all voucher batches
DELETE FROM voucher_batches;
ALTER TABLE voucher_batches AUTO_INCREMENT = 1;

-- Remove all vouchers
DELETE FROM vouchers;
ALTER TABLE vouchers AUTO_INCREMENT = 1;

-- Remove all voucher logs
DELETE FROM voucher_logs;
ALTER TABLE voucher_logs AUTO_INCREMENT = 1;

-- ============================================================
-- STEP 3: CLEAN USER-RELATED DATA (KEEP ONLY ADMIN ACCOUNTS)
-- ============================================================

-- Remove all notifications
DELETE FROM notifications;
ALTER TABLE notifications AUTO_INCREMENT = 1;

-- Remove all pending registrations
DELETE FROM pending_registrations;
ALTER TABLE pending_registrations AUTO_INCREMENT = 1;

-- Remove all audit logs
DELETE FROM audit_logs;
ALTER TABLE audit_logs AUTO_INCREMENT = 1;

-- Remove all resource locks
DELETE FROM resource_locks;
ALTER TABLE resource_locks AUTO_INCREMENT = 1;

-- Remove all non-admin users (keep only admin and system admin accounts)
DELETE FROM users WHERE role NOT IN ('admin') OR department NOT IN ('SYSTEM ADMIN', 'ADMIN');

-- ============================================================
-- STEP 4: CLEAN SYSTEM DATA
-- ============================================================

-- Remove all blacklisted voucher IDs
DELETE FROM blacklisted_voucher_ids;
ALTER TABLE blacklisted_voucher_ids AUTO_INCREMENT = 1;

-- ============================================================
-- STEP 5: RESET SYSTEM SETTINGS FOR PRODUCTION
-- ============================================================

-- Update system settings for production deployment
UPDATE system_settings SET 
  current_fiscal_year = YEAR(CURDATE()),
  system_time = NOW(),
  auto_backup_enabled = TRUE,
  session_timeout = 30,
  last_backup_date = NULL
WHERE id = 1;

-- ============================================================
-- STEP 6: ENSURE ESSENTIAL ADMIN USER EXISTS
-- ============================================================

-- Insert or update the main admin user (password: enter123)
INSERT INTO users (id, name, password, role, department, date_created, is_active, email)
VALUES (
  'admin-user-1',
  'ADMIN',
  '$2b$10$3euPcmQFCiblsZeEu5s7p.9MbGcD3wlf/U5OqOvdz3uyFwg0Pzv0K',
  'admin',
  'SYSTEM ADMIN',
  NOW(),
  TRUE,
  '<EMAIL>'
) ON DUPLICATE KEY UPDATE 
  name = 'ADMIN',
  password = '$2b$10$3euPcmQFCiblsZeEu5s7p.9MbGcD3wlf/U5OqOvdz3uyFwg0Pzv0K',
  role = 'admin',
  department = 'SYSTEM ADMIN',
  is_active = TRUE,
  email = '<EMAIL>';

-- ============================================================
-- STEP 7: RE-ENABLE FOREIGN KEY CHECKS
-- ============================================================
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================
-- STEP 8: VERIFY CLEANUP RESULTS
-- ============================================================

-- Show cleanup summary
SELECT 'CLEANUP SUMMARY' as Status;
SELECT 'Vouchers remaining:' as Item, COUNT(*) as Count FROM vouchers;
SELECT 'Batches remaining:' as Item, COUNT(*) as Count FROM voucher_batches;
SELECT 'Provisional cash records remaining:' as Item, COUNT(*) as Count FROM provisional_cash_records;
SELECT 'Users remaining:' as Item, COUNT(*) as Count FROM users;
SELECT 'Admin users:' as Item, COUNT(*) as Count FROM users WHERE role = 'admin';
SELECT 'Notifications remaining:' as Item, COUNT(*) as Count FROM notifications;
SELECT 'Audit logs remaining:' as Item, COUNT(*) as Count FROM audit_logs;

-- Show remaining users
SELECT 'REMAINING USERS:' as Status;
SELECT id, name, role, department, is_active, date_created FROM users ORDER BY role, name;

-- Show system settings
SELECT 'SYSTEM SETTINGS:' as Status;
SELECT * FROM system_settings;

-- ============================================================
-- DEPLOYMENT READY CONFIRMATION
-- ============================================================
SELECT '============================================================' as Message;
SELECT 'VMS DATABASE CLEANED FOR PRODUCTION DEPLOYMENT' as Message;
SELECT '============================================================' as Message;
SELECT 'Status: READY FOR DEPLOYMENT' as Message;
SELECT 'Admin User: ADMIN (password: enter123)' as Message;
SELECT 'All voucher data: REMOVED' as Message;
SELECT 'All user data: REMOVED (except admin)' as Message;
SELECT 'System settings: RESET TO PRODUCTION DEFAULTS' as Message;
SELECT '============================================================' as Message;

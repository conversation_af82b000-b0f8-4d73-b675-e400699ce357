import { ClearanceRemark, Currency, Department, Notification, ProvisionalCashRecord, TaxType, TransactionStatus, User, Voucher } from "./types";

// Mock data for development
export const departments: Department[] = [
  "FINANCE",
  "MINISTRIES",
  "PENSIONS",
  "PENTMEDIA",
  "MISSIONS",
  "PENTSOS",
  "AUDIT",
  "SYSTEM ADMIN"
];

export const taxTypes: TaxType[] = [
  "GOODS 3%",
  "SERVICE 7.5%",
  "WORKS 5%",
  "RENT 8%",
  "PCC 12.5%",
  "RISK 5%",
  "VEH.MAINT 10%",
  "OTHER"
];

export const currencies: Currency[] = [
  "GHS",
  "USD",
  "GBP",
  "EUR",
  "CFA"
];

// Helper function to ensure all users have the required fields
const createCompleteUser = (user: Partial<User>): User => {
  // Default password is department name + 123 (e.g., FINANCE123)
  const defaultPassword = `${user.department}123`;

  return {
    id: user.id || `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    name: user.name || '',
    department: user.department || '',
    role: user.role || 'USER',
    email: `${user.name?.toLowerCase().replace(/\s+/g, '.')}@${user.department?.toLowerCase()}.com`,
    password: user.password || defaultPassword,
    dateCreated: new Date().toISOString(),
    isActive: true,
    ...user
  } as User;
};

// Mock users - removed for production, users come from database
export const users: User[] = [];

// PRODUCTION: No test vouchers - all data comes from database
export const testVouchersForAllDepartments: any[] = [];

// Keep original empty for production
export const initialVouchers: Voucher[] = [];

// Initial provisional cash records - PRODUCTION: Keep empty for clean system
export const initialProvisionalCashRecords: ProvisionalCashRecord[] = [];

// PRODUCTION: No initial notifications - all notifications come from database
export const initialNotifications: Notification[] = [];

// ARCHITECTURAL FIX: Removed generateVoucherId function
// All voucher ID generation now happens server-side for consistency

// Calculate clearance remark
export type ClearanceResult = {
  remark: ClearanceRemark;
  difference: number;
}

export const calculateClearanceRemark = (mainAmount: number, amountRetired: number): ClearanceResult => {
  const difference = Math.abs(mainAmount - amountRetired);

  if (mainAmount === amountRetired) {
    return { remark: "CLEARED", difference: 0 };
  } else if (amountRetired > mainAmount) {
    return { remark: "DUE STAFF", difference };
  } else {
    return { remark: "REFUNDED TO CHEST", difference };
  }
};

import { useAppStore } from '@/lib/store';
import { NotificationsMenu } from '@/components/notifications';
import { UserNav } from '@/components/user-nav';
import { ModeToggle } from '@/components/mode-toggle';
import { ExitButton } from '@/components/exit-button';
import { DashboardFooter } from '@/components/dashboard/dashboard-footer';
import { AuditNavigation } from '@/components/audit-dashboard/audit-navigation';
import { AnalyticsDashboard } from '@/components/analytics/analytics-dashboard';

export default function AuditAnalytics() {
  const currentUser = useAppStore((state) => state.currentUser);

  if (!currentUser) {
    return null;
  }

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center px-4 sm:px-6">
          <h1 className="text-lg font-semibold uppercase">
            AUDIT ANALYTICS DASHBOARD
          </h1>
          <div className="ml-auto flex items-center space-x-4">
            <NotificationsMenu />
            <ModeToggle />
            <UserNav />
            <ExitButton />
          </div>
        </div>
      </header>

      <main className="flex-1 py-6 px-4 sm:px-6">
        <AuditNavigation />
        <AnalyticsDashboard />
      </main>

      <DashboardFooter />
    </div>
  );
}

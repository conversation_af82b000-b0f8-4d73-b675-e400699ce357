/**
 * CLIENT-SIDE SERVER DISCOVERY SYSTEM
 * Automatically finds VMS server on the network
 */

export interface ServerInfo {
  port: number;
  host: string;
  url: string;
  lastSeen?: number;
}

export interface DiscoveryConfig {
  broadcastPort: number;
  discoveryTimeout: number;
  retryInterval: number;
  maxRetries: number;
}

class ServerDiscovery {
  private config: DiscoveryConfig;
  private currentServer: ServerInfo | null = null;
  private discoveryCallbacks: ((server: ServerInfo) => void)[] = [];
  private connectionLostCallbacks: (() => void)[] = [];
  private isDiscovering = false;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor(config: Partial<DiscoveryConfig> = {}) {
    this.config = {
      broadcastPort: 8888,
      discoveryTimeout: 10000,
      retryInterval: 5000,
      maxRetries: 10,
      ...config
    };
  }

  /**
   * Start server discovery process
   */
  async startDiscovery(): Promise<ServerInfo> {
    console.log('🔍 Starting VMS server discovery...');
    
    return new Promise((resolve, reject) => {
      if (this.isDiscovering) {
        console.log('⚠️ Discovery already in progress');
        return;
      }

      this.isDiscovering = true;
      let attempts = 0;

      const tryDiscovery = async () => {
        attempts++;
        console.log(`🔍 Discovery attempt ${attempts}/${this.config.maxRetries}`);

        try {
          // Method 1: Try UDP broadcast discovery
          const server = await this.discoverViaUDP();
          if (server) {
            this.handleServerFound(server);
            resolve(server);
            return;
          }
        } catch (error) {
          console.warn('UDP discovery failed:', error);
        }

        try {
          // Method 2: Try known server info files
          const server = await this.discoverViaFiles();
          if (server) {
            this.handleServerFound(server);
            resolve(server);
            return;
          }
        } catch (error) {
          console.warn('File discovery failed:', error);
        }

        try {
          // Method 3: Try common ports on local network
          const server = await this.discoverViaPortScan();
          if (server) {
            this.handleServerFound(server);
            resolve(server);
            return;
          }
        } catch (error) {
          console.warn('Port scan discovery failed:', error);
        }

        // Retry if not found
        if (attempts < this.config.maxRetries) {
          setTimeout(tryDiscovery, this.config.retryInterval);
        } else {
          this.isDiscovering = false;
          reject(new Error('❌ Could not discover VMS server after maximum attempts'));
        }
      };

      tryDiscovery();
    });
  }

  /**
   * Discover server via UDP broadcast
   */
  private async discoverViaUDP(): Promise<ServerInfo | null> {
    return new Promise((resolve) => {
      // Note: UDP discovery requires a native implementation or WebRTC
      // For browser environment, we'll skip this method
      if (typeof window !== 'undefined') {
        resolve(null);
        return;
      }

      const dgram = require('dgram');
      const socket = dgram.createSocket('udp4');

      const timeout = setTimeout(() => {
        socket.close();
        resolve(null);
      }, this.config.discoveryTimeout);

      socket.on('message', (msg: Buffer) => {
        try {
          const data = JSON.parse(msg.toString());
          if (data.type === 'VMS_SERVER_DISCOVERY' && data.serverInfo) {
            clearTimeout(timeout);
            socket.close();
            resolve(data.serverInfo);
          }
        } catch (error) {
          console.warn('Invalid UDP message:', error);
        }
      });

      socket.bind(this.config.broadcastPort);
    });
  }

  /**
   * Discover server via saved info files
   */
  private async discoverViaFiles(): Promise<ServerInfo | null> {
    const possibleLocations = [
      '/server-info.json',
      '/api/server-info',
      // Add more locations as needed
    ];

    for (const location of possibleLocations) {
      try {
        const response = await fetch(location);
        if (response.ok) {
          const serverInfo = await response.json();
          if (await this.validateServer(serverInfo)) {
            return serverInfo;
          }
        }
      } catch (error) {
        // Continue to next location
      }
    }

    return null;
  }

  /**
   * Discover server via port scanning common ports
   */
  private async discoverViaPortScan(): Promise<ServerInfo | null> {
    const commonPorts = [8080, 8081, 8082, 3000, 3001, 3002, 9000, 9001];
    const currentHost = window.location.hostname;
    
    // If we're already on a server, try that first
    if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
      for (const port of commonPorts) {
        const serverInfo = {
          host: currentHost,
          port,
          url: `http://${currentHost}:${port}`
        };
        
        if (await this.validateServer(serverInfo)) {
          return serverInfo;
        }
      }
    }

    // Try localhost
    for (const port of commonPorts) {
      const serverInfo = {
        host: 'localhost',
        port,
        url: `http://localhost:${port}`
      };
      
      if (await this.validateServer(serverInfo)) {
        return serverInfo;
      }
    }

    return null;
  }

  /**
   * Validate if a server is the VMS server
   */
  private async validateServer(serverInfo: ServerInfo): Promise<boolean> {
    try {
      const response = await fetch(`${serverInfo.url}/health`, {
        method: 'GET',
        timeout: 3000
      } as any);
      
      if (response.ok) {
        const health = await response.json();
        return health.status === 'healthy' && health.version;
      }
    } catch (error) {
      // Server not reachable
    }
    
    return false;
  }

  /**
   * Handle server found
   */
  private handleServerFound(server: ServerInfo): void {
    console.log(`✅ VMS Server discovered: ${server.url}`);
    this.currentServer = { ...server, lastSeen: Date.now() };
    this.isDiscovering = false;
    
    // Notify callbacks
    this.discoveryCallbacks.forEach(callback => callback(server));
    
    // Start health monitoring
    this.startHealthMonitoring();
  }

  /**
   * Start monitoring server health
   */
  private startHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      if (!this.currentServer) return;

      const isHealthy = await this.validateServer(this.currentServer);
      
      if (!isHealthy) {
        console.warn('⚠️ Server connection lost, attempting rediscovery...');
        this.handleConnectionLost();
      } else {
        this.currentServer.lastSeen = Date.now();
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Handle connection lost
   */
  private handleConnectionLost(): void {
    this.currentServer = null;
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    // Notify callbacks
    this.connectionLostCallbacks.forEach(callback => callback());
    
    // Attempt automatic rediscovery
    setTimeout(() => {
      this.startDiscovery().catch(error => {
        console.error('❌ Automatic rediscovery failed:', error);
      });
    }, 2000);
  }

  /**
   * Get current server info
   */
  getCurrentServer(): ServerInfo | null {
    return this.currentServer;
  }

  /**
   * Register callback for server discovery
   */
  onServerDiscovered(callback: (server: ServerInfo) => void): void {
    this.discoveryCallbacks.push(callback);
  }

  /**
   * Register callback for connection lost
   */
  onConnectionLost(callback: () => void): void {
    this.connectionLostCallbacks.push(callback);
  }

  /**
   * Stop discovery and monitoring
   */
  stop(): void {
    this.isDiscovering = false;
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }
}

// Export singleton instance
export const serverDiscovery = new ServerDiscovery();

// Auto-start discovery when module loads
if (typeof window !== 'undefined') {
  serverDiscovery.startDiscovery().catch(error => {
    console.error('❌ Initial server discovery failed:', error);
  });
}

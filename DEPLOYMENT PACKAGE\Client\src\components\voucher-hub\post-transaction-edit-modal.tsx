import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { toast } from '../ui/use-toast';
import { Voucher } from '../../types/voucher';
import { useAppStore } from '../../lib/store';
import { Save, X, AlertCircle, FileText } from 'lucide-react';

interface PostTransactionEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  voucher: Voucher | null;
  onSave: (voucherId: string, editData: any, auditReason: string) => Promise<void>;
  isSaving: boolean;
}

export const PostTransactionEditModal: React.FC<PostTransactionEditModalProps> = ({
  isOpen,
  onClose,
  voucher,
  onSave,
  isSaving
}) => {
  const [auditReason, setAuditReason] = useState('');
  const [editFormData, setEditFormData] = useState<Partial<Voucher>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Get users from the store
  const users = useAppStore((state) => state.users);

  // Get active AUDIT users for dropdowns
  const getAuditUsers = () => {
    return users
      .filter(user => user.isActive && user.department === 'AUDIT')
      .map(user => user.name.toUpperCase());
  };

  // Reset form when modal opens/closes or voucher changes
  useEffect(() => {
    if (isOpen && voucher) {
      setEditFormData({
        claimant: (voucher.claimant || '').toUpperCase(),
        description: (voucher.description || '').toUpperCase(),
        amount: voucher.amount || 0,
        preAuditedAmount: voucher.preAuditedAmount || 0,
        taxType: (voucher.taxType || '').toUpperCase(),
        taxAmount: voucher.taxAmount || 0,
        preAuditedBy: (voucher.preAuditedBy || '').toUpperCase(),
        certifiedBy: (voucher.certifiedBy || '').toUpperCase(),
        currency: (voucher.currency || 'GHS').toUpperCase()
      });
      setAuditReason('');
      setValidationErrors({});
    } else if (!isOpen) {
      setEditFormData({});
      setAuditReason('');
      setValidationErrors({});
    }
  }, [isOpen, voucher]);

  const handleInputChange = (field: string, value: any) => {
    // Convert text fields to uppercase
    const processedValue = (typeof value === 'string' &&
      ['claimant', 'description', 'taxType', 'preAuditedBy', 'certifiedBy', 'currency'].includes(field))
      ? value.toUpperCase()
      : value;

    setEditFormData(prev => ({
      ...prev,
      [field]: processedValue
    }));

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Validate audit reason
    if (!auditReason.trim()) {
      errors.auditReason = 'Audit reason is required for post-transaction edits';
    } else if (auditReason.trim().length < 10) {
      errors.auditReason = 'Please provide a more detailed reason (minimum 10 characters)';
    }

    // Validate required fields
    if (!editFormData.claimant?.trim()) {
      errors.claimant = 'Claimant is required';
    }

    if (!editFormData.amount || editFormData.amount <= 0) {
      errors.amount = 'Amount must be greater than 0';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = async () => {
    if (!voucher) return;

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before saving",
        variant: "destructive"
      });
      return;
    }

    try {
      await onSave(voucher.id, editFormData, auditReason.trim());
      onClose();
      toast({
        title: "Success",
        description: "Voucher updated successfully",
        variant: "default"
      });
    } catch (error) {
      console.error('Error saving voucher:', error);
      toast({
        title: "Error",
        description: "Failed to save voucher changes",
        variant: "destructive"
      });
    }
  };

  const handleClose = () => {
    if (isSaving) return; // Prevent closing while saving
    onClose();
  };

  if (!voucher) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-100 border-slate-300">
        <DialogHeader className="bg-slate-200 -m-6 mb-6 p-6 border-b border-slate-300">
          <DialogTitle className="flex items-center gap-2 text-xl text-slate-800">
            <FileText className="h-5 w-5 text-blue-600" />
            Post-Transaction Edit - {voucher.voucherId}
            <Badge variant="outline" className="ml-2 bg-blue-100 text-blue-800 border-blue-300">
              {voucher.status}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Previous Edit History Section */}
          {voucher.lastEditReason && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <FileText className="h-5 w-5 text-blue-600" />
                <Label className="text-base font-semibold text-blue-800">
                  Previous Edit History
                </Label>
              </div>
              <div className="bg-blue-100 rounded p-3 text-sm">
                <div className="font-medium text-blue-900 mb-1">
                  Last edited by: {voucher.lastEditedBy || 'Unknown'}
                  {voucher.lastEditTime && (
                    <span className="text-blue-700 ml-2">
                      on {new Date(voucher.lastEditTime).toLocaleString()}
                    </span>
                  )}
                </div>
                <div className="text-blue-800">
                  <strong>Reason:</strong> {voucher.lastEditReason}
                </div>
              </div>
            </div>
          )}

          {/* Current Audit Reason Section */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <AlertCircle className="h-5 w-5 text-amber-600" />
              <Label className="text-base font-semibold text-amber-800">
                New Edit Reason <span className="text-red-500">*</span>
              </Label>
            </div>
            <Textarea
              value={auditReason}
              onChange={(e) => {
                setAuditReason(e.target.value);
                if (validationErrors.auditReason) {
                  setValidationErrors(prev => ({ ...prev, auditReason: '' }));
                }
              }}
              placeholder="Please provide a detailed reason for this post-transaction edit (e.g., 'Correcting vendor name due to data entry error', 'Updating amount based on revised invoice')..."
              className="min-h-[80px] resize-none bg-amber-100 border-amber-300 text-slate-800 placeholder:text-amber-600"
              disabled={isSaving}
            />
            {validationErrors.auditReason && (
              <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {validationErrors.auditReason}
              </p>
            )}
          </div>

          <Separator />

          {/* Editable Fields Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Edit Voucher Fields
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Claimant */}
              <div>
                <Label htmlFor="claimant" className="text-sm font-medium text-gray-700">
                  Claimant <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="claimant"
                  value={editFormData.claimant || ''}
                  onChange={(e) => handleInputChange('claimant', e.target.value)}
                  className={`mt-1 bg-slate-200 border-slate-400 text-slate-800 ${validationErrors.claimant ? 'border-red-500' : ''}`}
                  disabled={isSaving}
                  placeholder={(voucher.claimant || 'ENTER CLAIMANT NAME').toUpperCase()}
                />
                {validationErrors.claimant && (
                  <p className="text-sm text-red-600 mt-1">{validationErrors.claimant}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <Label htmlFor="description" className="text-sm font-medium text-gray-700">Description</Label>
                <Input
                  id="description"
                  value={editFormData.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="mt-1 bg-slate-200 border-slate-400 text-slate-800"
                  disabled={isSaving}
                  placeholder={(voucher.description || 'ENTER DESCRIPTION').toUpperCase()}
                />
              </div>

              {/* Amount */}
              <div>
                <Label htmlFor="amount" className="text-sm font-medium text-gray-700">
                  Amount <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  value={editFormData.amount || ''}
                  onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
                  className={`mt-1 bg-slate-200 border-slate-400 text-slate-800 ${validationErrors.amount ? 'border-red-500' : ''}`}
                  disabled={isSaving}
                  placeholder={voucher.amount?.toString() || '0.00'}
                />
                {validationErrors.amount && (
                  <p className="text-sm text-red-600 mt-1">{validationErrors.amount}</p>
                )}
              </div>

              {/* Currency */}
              <div>
                <Label htmlFor="currency" className="text-sm font-medium text-gray-700">Currency</Label>
                <Select
                  value={editFormData.currency || 'GHS'}
                  onValueChange={(value) => handleInputChange('currency', value)}
                  disabled={isSaving}
                >
                  <SelectTrigger className="mt-1 bg-slate-200 border-slate-400 text-slate-800">
                    <SelectValue placeholder={(voucher.currency || 'SELECT CURRENCY').toUpperCase()} className="text-slate-800" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-200 border-slate-400 text-slate-800">
                    <SelectItem value="GHS" className="text-slate-800 hover:bg-slate-300">GHS</SelectItem>
                    <SelectItem value="USD" className="text-slate-800 hover:bg-slate-300">USD</SelectItem>
                    <SelectItem value="EUR" className="text-slate-800 hover:bg-slate-300">EUR</SelectItem>
                    <SelectItem value="GBP" className="text-slate-800 hover:bg-slate-300">GBP</SelectItem>
                    <SelectItem value="CFA" className="text-slate-800 hover:bg-slate-300">CFA</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Pre-Audited Amount */}
              <div>
                <Label htmlFor="preAuditedAmount" className="text-sm font-medium text-gray-700">Pre-Audited Amount</Label>
                <Input
                  id="preAuditedAmount"
                  type="number"
                  step="0.01"
                  value={editFormData.preAuditedAmount || ''}
                  onChange={(e) => handleInputChange('preAuditedAmount', parseFloat(e.target.value) || 0)}
                  className="mt-1 bg-slate-200 border-slate-400 text-slate-800"
                  disabled={isSaving}
                  placeholder={voucher.preAuditedAmount?.toString() || '0.00'}
                />
              </div>

              {/* Tax Type */}
              <div>
                <Label htmlFor="taxType" className="text-sm font-medium text-gray-700">Tax Type</Label>
                <Select
                  value={editFormData.taxType || ''}
                  onValueChange={(value) => handleInputChange('taxType', value)}
                  disabled={isSaving}
                >
                  <SelectTrigger className="mt-1 bg-slate-200 border-slate-400 text-slate-800">
                    <SelectValue placeholder={(voucher.taxType || 'SELECT TAX TYPE').toUpperCase()} className="text-slate-800" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-200 border-slate-400 text-slate-800">
                    <SelectItem value="" className="text-slate-800 hover:bg-slate-300">NONE</SelectItem>
                    <SelectItem value="GOODS 3%" className="text-slate-800 hover:bg-slate-300">GOODS 3%</SelectItem>
                    <SelectItem value="SERVICE 7.5%" className="text-slate-800 hover:bg-slate-300">SERVICE 7.5%</SelectItem>
                    <SelectItem value="WORKS 5%" className="text-slate-800 hover:bg-slate-300">WORKS 5%</SelectItem>
                    <SelectItem value="RENT 8%" className="text-slate-800 hover:bg-slate-300">RENT 8%</SelectItem>
                    <SelectItem value="PCC 12.5%" className="text-slate-800 hover:bg-slate-300">PCC 12.5%</SelectItem>
                    <SelectItem value="RISK 5%" className="text-slate-800 hover:bg-slate-300">RISK 5%</SelectItem>
                    <SelectItem value="VEH.MAINT 10%" className="text-slate-800 hover:bg-slate-300">VEH.MAINT 10%</SelectItem>
                    <SelectItem value="OTHER" className="text-slate-800 hover:bg-slate-300">OTHER</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Tax Amount */}
              <div>
                <Label htmlFor="taxAmount" className="text-sm font-medium text-gray-700">Tax Amount</Label>
                <Input
                  id="taxAmount"
                  type="number"
                  step="0.01"
                  value={editFormData.taxAmount || ''}
                  onChange={(e) => handleInputChange('taxAmount', parseFloat(e.target.value) || 0)}
                  className="mt-1 bg-slate-200 border-slate-400 text-slate-800"
                  disabled={isSaving}
                  placeholder={voucher.taxAmount?.toString() || '0.00'}
                />
              </div>

              {/* Pre-Audited By */}
              <div>
                <Label htmlFor="preAuditedBy" className="text-sm font-medium text-gray-700">Pre-Audited By</Label>
                <Select
                  value={editFormData.preAuditedBy || ''}
                  onValueChange={(value) => handleInputChange('preAuditedBy', value)}
                  disabled={isSaving}
                >
                  <SelectTrigger className="mt-1 bg-slate-200 border-slate-400 text-slate-800">
                    <SelectValue placeholder={(voucher.preAuditedBy || 'SELECT PRE-AUDITOR').toUpperCase()} className="text-slate-800" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-200 border-slate-400 text-slate-800">
                    <SelectItem value="" className="text-slate-800 hover:bg-slate-300">NONE</SelectItem>
                    {getAuditUsers().map((userName) => (
                      <SelectItem key={userName} value={userName} className="text-slate-800 hover:bg-slate-300">
                        {userName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Certified By */}
              <div>
                <Label htmlFor="certifiedBy" className="text-sm font-medium text-gray-700">Certified By</Label>
                <Select
                  value={editFormData.certifiedBy || ''}
                  onValueChange={(value) => handleInputChange('certifiedBy', value)}
                  disabled={isSaving}
                >
                  <SelectTrigger className="mt-1 bg-slate-200 border-slate-400 text-slate-800">
                    <SelectValue placeholder={(voucher.certifiedBy || 'SELECT CERTIFIER').toUpperCase()} className="text-slate-800" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-200 border-slate-400 text-slate-800">
                    <SelectItem value="" className="text-slate-800 hover:bg-slate-300">NONE</SelectItem>
                    {getAuditUsers().map((userName) => (
                      <SelectItem key={userName} value={userName} className="text-slate-800 hover:bg-slate-300">
                        {userName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-6 border-t border-slate-300 bg-slate-200 -m-6 mt-6 p-6">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSaving}
              className="flex items-center gap-2 bg-slate-300 hover:bg-slate-400 border-slate-500 text-slate-800"
            >
              <X className="h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
            >
              <Save className="h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

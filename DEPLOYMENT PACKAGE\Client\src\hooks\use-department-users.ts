import { Department, User } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { useMemo } from 'react';

export function useDepartmentUsers(department?: Department): User[] {
  // Get users from the store
  const users = useAppStore(state => state.users);

  // Memoize the filtered users to prevent infinite re-renders
  return useMemo(() => {
    if (!department) return [];

    // Filter users by department
    return users.filter(user => user.department === department);
  }, [users, department]);
}

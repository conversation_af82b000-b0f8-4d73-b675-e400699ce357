import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { createVouchersSlice, VouchersSlice } from './slices/vouchers-slice';
import { createAdminSlice, AdminSlice } from './slices/admin-slice';
import { createUsersSlice, UsersSlice } from './slices/users-slice';
import { createNotificationsSlice, NotificationsSlice } from './slices/notifications-slice';
import { createBlacklistedVoucherIdsSlice, BlacklistedVoucherIdsSlice } from './slices/blacklisted-voucher-ids-slice';
import { createVoucherBatchesSlice, VoucherBatchesSlice } from './slices/voucher-batches-slice';
import { createProvisionalCashRecordsSlice, ProvisionalCashRecordsSlice } from './slices/provisional-cash-records-slice';
import { createAuditSlice, AuditSlice } from './slices/audit-slice';
import { createDepartmentSlice, DepartmentSlice } from './slices/department-slice';
import { createCleanupSlice, CleanupSlice } from './slices/cleanup-slice';
import { createAuthSlice, AuthSlice } from './slices/auth-slice';

export interface AppState extends
  VouchersSlice,
  AdminSlice,
  UsersSlice,
  NotificationsSlice,
  BlacklistedVoucherIdsSlice,
  VoucherBatchesSlice,
  ProvisionalCashRecordsSlice,
  AuditSlice,
  DepartmentSlice,
  CleanupSlice,
  AuthSlice {
  // Real-time sync state
  lastRefreshTime: number;
  setLastRefreshTime: (timestamp: number) => void;
}

export const useStore = create<AppState>()(
  persist(
    (...a) => ({
      ...createVouchersSlice(...a),
      ...createAdminSlice(...a),
      ...createUsersSlice(...a),
      ...createNotificationsSlice(...a),
      ...createBlacklistedVoucherIdsSlice(...a),
      ...createVoucherBatchesSlice(...a),
      ...createProvisionalCashRecordsSlice(...a),
      ...createAuditSlice(...a),
      ...createDepartmentSlice(...a),
      ...createCleanupSlice(...a),
      ...createAuthSlice(...a),

      // Real-time sync state
      lastRefreshTime: Date.now(),
      setLastRefreshTime: (timestamp: number) => a[0]({ lastRefreshTime: timestamp }),
    }),
    {
      name: 'app-storage',
    }
  )
); 
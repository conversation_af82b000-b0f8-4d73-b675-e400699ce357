import { useState } from 'react';
import { Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { formatCurrentDate } from '@/lib/store/utils';
import { toast } from 'sonner';
import { vouchersApi } from '@/lib/api';

export const useVoucherEditing = () => {
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const addProvisionalCashRecord = useAppStore((state) => state.addProvisionalCashRecord);
  const loadProvisionalCashRecords = useAppStore((state) => state.loadProvisionalCashRecords);
  const vouchers = useAppStore((state) => state.vouchers);
  const currentUser = useAppStore((state) => state.currentUser);

  const [voucherEdits, setVoucherEdits] = useState<Record<string, Partial<any>>>({});


  const handleVoucherEdit = (voucherId: string, field: string, value: any) => {
    console.log(`Editing voucher ${voucherId}, field: ${field}, value:`, value);



    // Handle null field - clear edits
    if (field === null) {
      setVoucherEdits(prev => {
        const newEdits = { ...prev };
        delete newEdits[voucherId];
        return newEdits;
      });
      return;
    }

    // Process comment fields - ensure they're strings
    if (field === 'comment' || field === 'returnComment') {
      if (value !== undefined && value !== null) {
        value = String(value);
      }
    }

    setVoucherEdits(prev => ({
      ...prev,
      [voucherId]: {
        ...(prev[voucherId] || {}),
        [field]: value
      }
    }));
  };

  const handleSaveVoucherEdits = async (voucherId: string) => {
    if (!voucherEdits[voucherId]) {
      console.log(`No edits found for voucher ${voucherId}`);
      return;
    }

    console.log(`Saving edits for voucher ${voucherId}:`, voucherEdits[voucherId]);

    try {


      // PENDING TAB EDITING FIX: Get original voucher to preserve metadata
      const originalVoucher = vouchers.find(v => v.id === voucherId);
      if (!originalVoucher) {
        console.error(`Original voucher ${voucherId} not found`);
        return;
      }

      // Make a copy of the edits to ensure we don't modify the original
      const editsToApply = { ...voucherEdits[voucherId] };

      // PENDING TAB EDITING FIX: Preserve critical metadata fields
      // Only include these fields if they're not already in the edits
      const metadataFields = ['createdBy', 'created_by', 'createdAt', 'created_at', 'voucherId', 'voucher_id'];
      metadataFields.forEach(field => {
        if (originalVoucher[field] && !editsToApply.hasOwnProperty(field)) {
          editsToApply[field] = originalVoucher[field];
        }
      });

      console.log(`🔧 PENDING TAB EDITING: Preserving metadata for voucher ${voucherId}:`, {
        createdBy: editsToApply.createdBy || editsToApply.created_by,
        voucherId: editsToApply.voucherId || editsToApply.voucher_id
      });

      // CRITICAL FIX: Set workStarted flag when any work is done on the voucher
      // This moves the voucher from NEW VOUCHERS to PENDING DISPATCH
      if (editsToApply.preAuditedAmount !== undefined ||
          editsToApply.preAuditedBy ||
          editsToApply.certifiedBy ||
          editsToApply.pendingReturn ||
          editsToApply.postProvisionalCash) {
        editsToApply.workStarted = true;
      }

      // Ensure critical fields are set
      // If status is not explicitly set, default to AUDIT: PROCESSING when key fields are edited
      if (!editsToApply.status &&
          (editsToApply.preAuditedAmount !== undefined ||
           editsToApply.preAuditedBy ||
           editsToApply.certifiedBy)) {
        editsToApply.status = "AUDIT: PROCESSING";
      }

      // Ensure it's not marked as dispatched
      if (editsToApply.status === "AUDIT: PROCESSING") {
        editsToApply.dispatched = false;
      }

      // Ensure preAuditedAmount is a number if provided
      if (editsToApply.preAuditedAmount !== undefined) {
        if (typeof editsToApply.preAuditedAmount === 'string') {
          editsToApply.preAuditedAmount = parseFloat(editsToApply.preAuditedAmount);
        }
      }

      // Ensure taxAmount is a number if provided
      if (editsToApply.taxAmount !== undefined && typeof editsToApply.taxAmount === 'string') {
        editsToApply.taxAmount = parseFloat(editsToApply.taxAmount);
      }

      // CRITICAL FIX: Always set workStarted flag when saving edits in audit
      if (editsToApply.preAuditedAmount !== undefined ||
          editsToApply.preAuditedBy ||
          editsToApply.certifiedBy ||
          editsToApply.comment ||
          editsToApply.postProvisionalCash) {
        editsToApply.workStarted = true;
      }

      // Log final edits before applying
      console.log('Final edits being applied:', editsToApply);

      // REAL-TIME TAB MOVEMENT FIX: Ensure voucher will appear in PENDING DISPATCH tab
      console.log('Ensuring voucher will appear in PENDING DISPATCH tab');

      // Regular voucher update
      await updateVoucher(voucherId, editsToApply);

      // REAL-TIME TAB MOVEMENT FIX: Force immediate UI update for tab movement
      // This ensures the voucher moves to PENDING DISPATCH immediately
      setTimeout(() => {
        // Dispatch custom event to trigger immediate tab refresh
        window.dispatchEvent(new CustomEvent('voucherUpdated', {
          detail: {
            voucherId,
            type: 'workStarted',
            workStarted: true,
            edits: editsToApply
          }
        }));
        console.log(`🔄 REAL-TIME: Dispatched voucherUpdated event for immediate tab movement`);
      }, 10);

      // Check if provisional cash record needs to be created
      if (editsToApply.postProvisionalCash) {
        const voucher = vouchers.find(v => v.id === voucherId);
        if (voucher) {
          try {
            await addProvisionalCashRecord({
              voucherId: voucher.id,
              voucherRef: voucher.voucherId,
              claimant: voucher.claimant,
              description: voucher.description,
              mainAmount: voucher.preAuditedAmount || voucher.amount,
              currency: voucher.currency,
              date: formatCurrentDate()
            });
            console.log('✅ Provisional cash record created successfully');

            // REAL-TIME FIX: Immediately refresh provisional cash records
            try {
              await loadProvisionalCashRecords();
              console.log('✅ Provisional cash records refreshed in real-time');

              // Show success notification
              toast('✅ Provisional cash record created successfully', {
                description: `Record created for ${voucher.claimant} - ${voucher.voucherId}`,
                duration: 4000,
              });
            } catch (refreshError) {
              console.error('❌ Failed to refresh provisional cash records:', refreshError);
            }
          } catch (error) {
            console.error('❌ Failed to create provisional cash record:', error);
            toast('Failed to create provisional cash record', {
              description: 'Please try again or contact support.',
            });
          }
        }
      }

      // Clear edits after saving
      setVoucherEdits(prev => {
        const newEdits = { ...prev };
        delete newEdits[voucherId];
        return newEdits;
      });



      // Verification: log the voucher state after update
      setTimeout(() => {
        const voucher = vouchers.find(v => v.id === voucherId);
        if (voucher) {
          console.log(`Verification - Voucher ${voucherId} after save:`, {
            id: voucher.id,
            status: voucher.status,
            preAuditedAmount: voucher.preAuditedAmount,
            preAuditedBy: voucher.preAuditedBy,
            certifiedBy: voucher.certifiedBy
          });
        }
      }, 50);

    } catch (error) {
      console.error('Error saving voucher edits:', error);
      toast.error('Failed to save voucher edits', {
        duration: 3000,
      });
    }
  };



  const handleMarkForRejection = (voucherId: string, comment: string) => {
    const voucher = vouchers.find(v => v.id === voucherId);
    if (!voucher) return;

    const currentTime = formatCurrentDate();

    // Ensure comment is a string
    const stringComment = String(comment || "NO COMMENT PROVIDED");

    console.log(`Marking voucher ${voucherId} for rejection with comment: "${stringComment}"`);

    // Update the existing voucher with rejection status and comment
    const updateData: Partial<Voucher> = {
      status: "VOUCHER REJECTED",
      comment: stringComment,
      rejectedBy: currentUser?.name || "Unknown User",
      rejectionTime: currentTime,
      deleted: false,
      sentToAudit: true
    };

    // Update the voucher in the store
    updateVoucher(voucherId, updateData);

    // REAL-TIME TAB MOVEMENT FIX: Force immediate UI update for rejection
    setTimeout(() => {
      // Dispatch custom event to trigger immediate tab refresh
      window.dispatchEvent(new CustomEvent('voucherUpdated', {
        detail: {
          voucherId,
          type: 'rejected',
          status: 'VOUCHER REJECTED',
          comment: stringComment
        }
      }));
      console.log(`🔄 REAL-TIME: Dispatched voucherUpdated event for immediate REJECTED tab movement`);
    }, 10);

    toast.success('VOUCHER MARKED FOR REJECTION', {
      duration: 3000,
    });
  };

  const handleReturnToNew = (voucherId: string) => {
    const updateData: Partial<Voucher> = {
      preAuditedAmount: undefined,
      taxType: undefined,
      taxAmount: undefined,
      postProvisionalCash: undefined,
      preAuditedBy: undefined,
      certifiedBy: undefined,
      pendingReturn: false,
      returnComment: undefined,
      workStarted: false  // CRITICAL FIX: Reset workStarted to move voucher back to NEW VOUCHERS tab
    };

    updateVoucher(voucherId, updateData);

    // REAL-TIME TAB MOVEMENT FIX: Force immediate UI update for return to new
    setTimeout(() => {
      // Dispatch custom event to trigger immediate tab refresh
      window.dispatchEvent(new CustomEvent('voucherUpdated', {
        detail: {
          voucherId,
          type: 'returned_to_new',
          workStarted: false
        }
      }));
      console.log(`🔄 REAL-TIME: Dispatched voucherUpdated event for immediate NEW VOUCHERS tab movement`);
    }, 10);

    toast.success('VOUCHER RETURNED TO NEW VOUCHERS', {
      duration: 3000,
    });
  };

  return {
    voucherEdits,
    setVoucherEdits,

    handleVoucherEdit,
    handleSaveVoucherEdits,

    handleMarkForRejection,
    handleReturnToNew
  };
};

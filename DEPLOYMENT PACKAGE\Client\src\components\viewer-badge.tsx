import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Eye } from 'lucide-react';
import { ResourceViewer } from '@/lib/store/types';

interface ViewerBadgeProps {
  viewers: ResourceViewer[];
  viewerCount: number;
  isViewing: boolean;
}

export function ViewerBadge({ viewers, viewerCount, isViewing }: ViewerBadgeProps) {
  if (viewerCount === 0) {
    return null;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant={isViewing ? "default" : "outline"} 
            className={`${isViewing ? 'bg-blue-600' : 'border-blue-600 text-blue-600'} cursor-pointer`}
          >
            <Eye className="h-3 w-3 mr-1" />
            {viewerCount} {viewerCount === 1 ? 'VIEWER' : 'VIEWERS'}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <p className="font-semibold">Current Viewers:</p>
            {viewers.length > 0 ? (
              <ul className="text-sm">
                {viewers.map((viewer) => (
                  <li key={viewer.userId}>
                    {viewer.userName} ({viewer.department})
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm">No active viewers</p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

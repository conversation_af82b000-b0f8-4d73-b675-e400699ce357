import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Download,
  Eye,
  Printer,
  Trash2,
  FileText,
  Image as ImageIcon,
  Calendar,
  User,
  AlertCircle,
  Upload,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';
import { usersApi } from '@/lib/api';
import { AuditAttachment } from '@/lib/store/types';

interface AttachmentViewerProps {
  voucherId: string;
  onAttachmentsChange?: () => void;
  readOnly?: boolean;
}

export function AttachmentViewer({
  voucherId,
  onAttachmentsChange,
  readOnly = false
}: AttachmentViewerProps) {
  const [attachments, setAttachments] = useState<AuditAttachment[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchAttachments();
  }, [voucherId]);

  const fetchAttachments = async () => {
    try {
      setLoading(true);
      const data = await usersApi.getVoucherAttachments(voucherId);
      setAttachments(data);
    } catch (error: any) {
      console.error('Error fetching attachments:', error);
      if (error.response?.status !== 403) {
        toast.error('Failed to load attachments');
      }
      setAttachments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (attachment: AuditAttachment) => {
    try {
      setActionLoading(attachment.id);
      const response = await usersApi.downloadAttachment(attachment.id);
      
      // Create blob and download
      const blob = new Blob([response.data], { type: attachment.mime_type });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = attachment.stored_filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success(`Downloaded ${attachment.stored_filename}`);
    } catch (error: any) {
      console.error('Download error:', error);
      toast.error('Failed to download file');
    } finally {
      setActionLoading(null);
    }
  };

  const handleView = async (attachment: AuditAttachment) => {
    try {
      setActionLoading(attachment.id);
      const response = await usersApi.viewAttachment(attachment.id);
      
      // Create blob URL and open in new tab
      const blob = new Blob([response.data], { type: attachment.mime_type });
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');
      
      // Clean up the URL after a delay
      setTimeout(() => window.URL.revokeObjectURL(url), 1000);
    } catch (error: any) {
      console.error('View error:', error);
      toast.error('Failed to view file');
    } finally {
      setActionLoading(null);
    }
  };

  const handlePrint = async (attachment: AuditAttachment) => {
    try {
      setActionLoading(attachment.id);
      const response = await usersApi.viewAttachment(attachment.id);
      
      // Create blob URL and open in new window for printing
      const blob = new Blob([response.data], { type: attachment.mime_type });
      const url = window.URL.createObjectURL(blob);
      const printWindow = window.open(url, '_blank');
      
      if (printWindow) {
        printWindow.onload = () => {
          printWindow.print();
        };
      }
      
      // Clean up the URL after a delay
      setTimeout(() => window.URL.revokeObjectURL(url), 5000);
    } catch (error: any) {
      console.error('Print error:', error);
      toast.error('Failed to print file');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async (attachment: AuditAttachment) => {
    if (!confirm(`Are you sure you want to delete "${attachment.stored_filename}"?`)) {
      return;
    }

    try {
      setActionLoading(attachment.id);
      await usersApi.deleteAttachment(attachment.id);
      toast.success('Attachment deleted successfully');
      fetchAttachments();
      onAttachmentsChange?.();
    } catch (error: any) {
      console.error('Delete error:', error);
      toast.error('Failed to delete attachment');
    } finally {
      setActionLoading(null);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);
    try {
      const result = await usersApi.uploadVoucherAttachments(voucherId, files);
      toast.success(`Successfully uploaded ${result.attachments.length} file(s)`);

      // Refresh attachments list
      await fetchAttachments();

      // Notify parent component
      if (onAttachmentsChange) {
        onAttachmentsChange();
      }

      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error('Failed to upload files');
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType === 'application/pdf') {
      return <FileText className="h-8 w-8 text-red-600" />;
    } else if (mimeType === 'image/jpeg') {
      return <ImageIcon className="h-8 w-8 text-blue-600" />;
    }
    return <FileText className="h-8 w-8 text-gray-600" />;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading attachments...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Attachments ({attachments.length})
          </div>
          {!readOnly && (
            <Button
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              {uploading ? 'Uploading...' : 'Add Files'}
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {attachments.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No attachments found for this voucher</p>
            {!readOnly && (
              <p className="text-sm mt-1">Click "Add Files" to attach documents</p>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {attachments.map((attachment) => (
              <div key={attachment.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    {getFileIcon(attachment.mime_type)}
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{attachment.stored_filename}</h4>
                      <p className="text-xs text-gray-500 mt-1">
                        Original: {attachment.original_filename}
                      </p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(attachment.uploaded_at)}
                        </span>
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {attachment.uploader_name || 'Unknown'}
                        </span>
                        <span>{formatFileSize(attachment.file_size)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleView(attachment)}
                      disabled={actionLoading === attachment.id}
                      title="View file"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDownload(attachment)}
                      disabled={actionLoading === attachment.id}
                      title="Download file"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePrint(attachment)}
                      disabled={actionLoading === attachment.id}
                      title="Print file"
                    >
                      <Printer className="h-4 w-4" />
                    </Button>
                    
                    {!readOnly && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(attachment)}
                        disabled={actionLoading === attachment.id}
                        title="Delete file"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                    
                    {actionLoading === attachment.id && (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 ml-2"></div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Hidden file input */}
        {!readOnly && (
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.jpg,.jpeg"
            onChange={handleFileUpload}
            className="hidden"
          />
        )}
      </CardContent>
    </Card>
  );
}


import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/lib/store';
import { Voucher } from '@/lib/types';

export function useDashboardState() {
  const navigate = useNavigate();
  const currentUser = useAppStore((state) => state.currentUser);
  const voucherBatches = useAppStore((state) =>
    currentUser ? state.getVoucherBatchesForDepartment(currentUser.department) : []
  );

  // Only count batches that are coming from audit, haven't been received yet,
  // and ensure batch is properly marked as not received (double-check)
  const departmentBatches = voucherBatches.filter(b =>
    b.fromAudit === true &&
    b.received === false
  );

  const [selectedVouchers, setSelectedVouchers] = useState<string[]>([]);
  const [showVoucherReceiving, setShowVoucherReceiving] = useState(false);
  const [receivingVoucherIds, setReceivingVoucherIds] = useState<string[]>([]);
  const [dispatchedBy, setDispatchedBy] = useState<string>('');
  const [customDispatchName, setCustomDispatchName] = useState<string>('');
  const [viewingVoucher, setViewingVoucher] = useState<Voucher | null>(null);
  const [showBatchReceiving, setShowBatchReceiving] = useState(false);
  const [selectedBatchId, setSelectedBatchId] = useState<string>('');
  const [voucherView, setVoucherView] = useState('pending-submission');
  const [isNotificationBlinking, setIsNotificationBlinking] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    if (!currentUser) {
      navigate('/');
      return;
    }
  }, [currentUser, navigate]);

  useEffect(() => {
    if (!currentUser) return;

    // Redirect to the appropriate dashboard based on department
    switch (currentUser.department) {
      case 'AUDIT':
        navigate('/audit-dashboard');
        break;
      case 'ADMINISTRATOR':
      case 'SYSTEM ADMIN':
        navigate('/admin-dashboard');
        break;
      case 'FINANCE':
        // Already on the right dashboard
        break;
      case 'MINISTRIES':
        navigate('/ministries-dashboard');
        break;
      case 'PENSIONS':
        navigate('/pensions-dashboard');
        break;
      case 'PENTMEDIA':
        navigate('/pentmedia-dashboard');
        break;
      case 'MISSIONS':
        navigate('/missions-dashboard');
        break;
      case 'PENTSOS':
        navigate('/pentsos-dashboard');
        break;
    }
  }, [currentUser, navigate]);

  const refreshData = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleDisabledFormClick = () => {
    setIsNotificationBlinking(true);
    setTimeout(() => {
      setIsNotificationBlinking(false);
    }, 3000);
  };

  return {
    currentUser,
    departmentBatches,
    selectedVouchers,
    setSelectedVouchers,
    showVoucherReceiving,
    setShowVoucherReceiving,
    receivingVoucherIds,
    setReceivingVoucherIds,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,
    viewingVoucher,
    setViewingVoucher,
    showBatchReceiving,
    setShowBatchReceiving,
    selectedBatchId,
    setSelectedBatchId,
    voucherView,
    setVoucherView,
    isNotificationBlinking,
    setIsNotificationBlinking,
    refreshTrigger,
    refreshData,
    handleDisabledFormClick
  };
}

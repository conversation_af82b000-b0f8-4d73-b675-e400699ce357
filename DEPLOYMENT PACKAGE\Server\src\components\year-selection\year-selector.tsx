import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Database, Users, FileText, TrendingUp, ArrowLeft } from 'lucide-react';
import { useAppStore } from '@/lib/store';
import { useNavigate } from 'react-router-dom';

interface YearData {
  year: number;
  voucherCount: number;
  totalAmount: number;
  departments: string[];
  isActive: boolean;
  lastActivity: string;
}

interface YearSelectorProps {
  onYearSelected: (year: number) => void;
  onReturn?: () => void; // Optional return callback
}

export function YearSelector({ onYearSelected, onReturn }: YearSelectorProps) {
  const [availableYears, setAvailableYears] = useState<YearData[]>([]);
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const currentUser = useAppStore((state) => state.currentUser);
  const navigate = useNavigate();

  // Fetch available years from API
  useEffect(() => {
    const fetchAvailableYears = async () => {
      try {
        const response = await fetch('/api/years/available', {
          credentials: 'include'
        });
        
        if (response.ok) {
          const years = await response.json();
          setAvailableYears(years);
          
          // Auto-select current year if available
          const currentYear = new Date().getFullYear();
          const currentYearData = years.find((y: YearData) => y.year === currentYear);
          if (currentYearData) {
            setSelectedYear(currentYear);
          }
        }
      } catch (error) {
        console.error('Error fetching available years:', error);
        // Fallback to current year
        const currentYear = new Date().getFullYear();
        setAvailableYears([{
          year: currentYear,
          voucherCount: 0,
          totalAmount: 0,
          departments: ['FINANCE', 'AUDIT'],
          isActive: true,
          lastActivity: new Date().toISOString()
        }]);
        setSelectedYear(currentYear);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAvailableYears();
  }, []);

  const handleYearSelection = (year: number) => {
    setSelectedYear(year);
  };

  const handleProceed = () => {
    if (selectedYear) {
      onYearSelected(selectedYear);
    }
  };

  const handleReturn = () => {
    if (onReturn) {
      onReturn();
    } else {
      // When used within YearAwareApp, we should restore the year selection
      // This will trigger YearAwareApp to show the main app again
      const currentYear = new Date().getFullYear();
      onYearSelected(currentYear);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatLastActivity = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <Card className="w-96">
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <Database className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
              <p className="text-lg font-medium">Loading VMS Data...</p>
              <p className="text-sm text-muted-foreground mt-2">Checking available years</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative">
      {/* Return Button - Extreme Top Right */}
      <div className="absolute top-4 right-4 z-50">
        <Button
          variant="outline"
          onClick={handleReturn}
          className="flex items-center gap-2 bg-white border-2 border-gray-300 text-gray-700 font-semibold px-4 py-2 shadow-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
        >
          <ArrowLeft className="h-4 w-4" />
          Return to Dashboard
        </Button>
      </div>

      {/* Main Content */}
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-4xl">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              VOUCHER MANAGEMENT SYSTEM
            </h1>
            <p className="text-lg text-gray-600">
              Welcome back, <span className="font-semibold">{currentUser?.name}</span>
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Select the operational year you want to work with
            </p>
          </div>

        {/* Year Selection - Clean List */}
        <div className="space-y-4 mb-8">
          {availableYears.map((yearData) => (
            <div
              key={yearData.year}
              className={`text-center p-6 rounded-lg cursor-pointer transition-all duration-200 ${
                selectedYear === yearData.year
                  ? 'bg-blue-100 border-2 border-blue-500'
                  : 'bg-white/50 hover:bg-white/70 border-2 border-transparent'
              }`}
              onClick={() => handleYearSelection(yearData.year)}
            >
              <div className="flex items-center justify-center gap-3">
                <Calendar className="h-6 w-6 text-blue-600" />
                <span className="text-2xl font-bold text-gray-900">{yearData.year}</span>
                {yearData.isActive && (
                  <Badge variant="default" className="bg-green-600">
                    Active
                  </Badge>
                )}
                {yearData.year === new Date().getFullYear() && (
                  <Badge variant="outline" className="border-blue-500 text-blue-600">
                    Current
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="text-center">
          <Button
            onClick={handleProceed}
            disabled={!selectedYear}
            size="lg"
            className="px-8 py-3 text-lg font-semibold"
          >
            {selectedYear ? `Access ${selectedYear} Data` : 'Select a Year'}
          </Button>

          {selectedYear && (
            <p className="text-sm text-gray-500 mt-3">
              You will be working with {selectedYear} operational data
            </p>
          )}
        </div>

          {/* Footer */}
          <div className="text-center mt-8 text-xs text-gray-400">
            <p>VMS Multi-Year Data Management System</p>
            <p>Secure • Reliable • Scalable</p>
          </div>
        </div>
      </div>
    </div>
  );
}

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function cleanupDatabase() {
  let connection;
  
  try {
    console.log('============================================================');
    console.log('VMS PRODUCTION DATABASE CLEANUP FOR DEPLOYMENT');
    console.log('============================================================');
    console.log('');
    
    // Create database connection (using same credentials as the server)
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989', // Correct password from server configuration
      database: 'vms_production',
      multipleStatements: true
    });
    
    console.log('✅ Connected to MySQL database');
    console.log('');
    
    console.log('🔄 Executing database cleanup...');
    console.log('');

    // Execute cleanup commands individually
    console.log('🔄 Disabling foreign key checks...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');

    console.log('🔄 Cleaning provisional cash records...');
    await connection.execute('DELETE FROM provisional_cash_records');

    console.log('🔄 Cleaning batch-voucher relationships...');
    await connection.execute('DELETE FROM batch_vouchers');

    console.log('🔄 Cleaning voucher batches...');
    await connection.execute('DELETE FROM voucher_batches');

    console.log('🔄 Cleaning vouchers...');
    await connection.execute('DELETE FROM vouchers');

    console.log('🔄 Cleaning voucher logs...');
    await connection.execute('DELETE FROM voucher_logs');

    console.log('🔄 Cleaning notifications...');
    await connection.execute('DELETE FROM notifications');

    console.log('🔄 Cleaning pending registrations...');
    await connection.execute('DELETE FROM pending_registrations');

    console.log('🔄 Cleaning audit logs...');
    await connection.execute('DELETE FROM audit_logs');

    console.log('🔄 Cleaning resource locks...');
    await connection.execute('DELETE FROM resource_locks');

    console.log('🔄 Removing non-admin users...');
    await connection.execute('DELETE FROM users WHERE role NOT IN ("admin") OR department NOT IN ("SYSTEM ADMIN", "ADMIN")');

    console.log('🔄 Cleaning blacklisted voucher IDs...');
    await connection.execute('DELETE FROM blacklisted_voucher_ids');

    console.log('🔄 Updating system settings...');
    await connection.execute(`
      UPDATE system_settings SET
        current_fiscal_year = YEAR(CURDATE()),
        system_time = NOW(),
        auto_backup_enabled = TRUE,
        session_timeout = 30,
        last_backup_date = NULL
      WHERE id = 1
    `);

    console.log('🔄 Ensuring admin user exists...');
    await connection.execute(`
      INSERT INTO users (id, name, password, role, department, date_created, is_active, email)
      VALUES (
        'admin-user-1',
        'ADMIN',
        '$2b$10$3euPcmQFCiblsZeEu5s7p.9MbGcD3wlf/U5OqOvdz3uyFwg0Pzv0K',
        'admin',
        'SYSTEM ADMIN',
        NOW(),
        TRUE,
        '<EMAIL>'
      ) ON DUPLICATE KEY UPDATE
        name = 'ADMIN',
        password = '$2b$10$3euPcmQFCiblsZeEu5s7p.9MbGcD3wlf/U5OqOvdz3uyFwg0Pzv0K',
        role = 'admin',
        department = 'SYSTEM ADMIN',
        is_active = TRUE,
        email = '<EMAIL>'
    `);

    console.log('🔄 Re-enabling foreign key checks...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    
    console.log('✅ Database cleanup completed successfully!');
    console.log('');
    
    // Verify cleanup by checking counts
    console.log('============================================================');
    console.log('CLEANUP VERIFICATION');
    console.log('============================================================');
    
    const [voucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    const [batchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [adminCount] = await connection.execute('SELECT COUNT(*) as count FROM users WHERE role = "admin"');
    const [notificationCount] = await connection.execute('SELECT COUNT(*) as count FROM notifications');
    
    console.log(`📊 Vouchers remaining: ${voucherCount[0].count}`);
    console.log(`📊 Batches remaining: ${batchCount[0].count}`);
    console.log(`📊 Users remaining: ${userCount[0].count}`);
    console.log(`📊 Admin users: ${adminCount[0].count}`);
    console.log(`📊 Notifications remaining: ${notificationCount[0].count}`);
    console.log('');
    
    // Show remaining users
    const [users] = await connection.execute('SELECT id, name, role, department, is_active FROM users ORDER BY role, name');
    console.log('👥 REMAINING USERS:');
    users.forEach(user => {
      console.log(`   - ${user.name} (${user.role}) - ${user.department} - Active: ${user.is_active}`);
    });
    console.log('');
    
    console.log('============================================================');
    console.log('🎉 VMS DATABASE CLEANED FOR PRODUCTION DEPLOYMENT');
    console.log('============================================================');
    console.log('Status: ✅ READY FOR DEPLOYMENT');
    console.log('Admin User: ADMIN (password: enter123)');
    console.log('All voucher data: ❌ REMOVED');
    console.log('All user data: ❌ REMOVED (except admin)');
    console.log('System settings: 🔄 RESET TO PRODUCTION DEFAULTS');
    console.log('============================================================');
    
  } catch (error) {
    console.error('❌ Database cleanup failed:', error.message);
    console.error('');
    console.error('Please check:');
    console.error('1. MySQL is running');
    console.error('2. Database credentials are correct');
    console.error('3. vms_production database exists');
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the cleanup
cleanupDatabase();

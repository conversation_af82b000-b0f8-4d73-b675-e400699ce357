/**
 * Workflow Action Button Component
 * Handles voucher workflow state transitions with confirmation dialogs
 */

import React, { useState } from 'react';
import { WorkflowEvent } from '../../lib/workflow/VoucherWorkflowStateMachine';

interface WorkflowActionButtonProps {
  event: WorkflowEvent;
  voucherId: string;
  onAction: (event: WorkflowEvent, voucherId: string, metadata?: any) => Promise<void>;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
}

export const WorkflowActionButton: React.FC<WorkflowActionButtonProps> = ({
  event,
  voucherId,
  onAction,
  disabled = false,
  loading = false,
  className = '',
  size = 'md',
  variant = 'primary'
}) => {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [reason, setReason] = useState('');
  const [metadata, setMetadata] = useState<any>({});

  const getActionConfig = (event: WorkflowEvent) => {
    const configs = {
      [WorkflowEvent.SUBMIT_TO_AUDIT]: {
        label: 'Submit to Audit',
        icon: '📤',
        requiresConfirmation: true,
        variant: 'primary' as const
      },
      [WorkflowEvent.START_WORK]: {
        label: 'Start Work',
        icon: '▶️',
        requiresConfirmation: false,
        variant: 'success' as const
      },
      [WorkflowEvent.CERTIFY_VOUCHER]: {
        label: 'Certify',
        icon: '✅',
        requiresConfirmation: true,
        variant: 'success' as const
      },
      [WorkflowEvent.REJECT_VOUCHER]: {
        label: 'Reject',
        icon: '❌',
        requiresConfirmation: true,
        requiresReason: true,
        variant: 'danger' as const
      },
      [WorkflowEvent.RETURN_VOUCHER]: {
        label: 'Return',
        icon: '↩️',
        requiresConfirmation: true,
        requiresReason: true,
        variant: 'secondary' as const
      },
      [WorkflowEvent.DISPATCH_TO_FINANCE]: {
        label: 'Dispatch',
        icon: '🚀',
        requiresConfirmation: true,
        variant: 'primary' as const
      },
      [WorkflowEvent.RESUBMIT_FROM_REJECTED]: {
        label: 'Resubmit',
        icon: '🔄',
        requiresConfirmation: true,
        requiresChangesDescription: true,
        variant: 'primary' as const
      },
      [WorkflowEvent.RESUBMIT_FROM_RETURNED]: {
        label: 'Resubmit',
        icon: '🔄',
        requiresConfirmation: true,
        requiresChangesDescription: true,
        variant: 'primary' as const
      }
    };

    return configs[event] || {
      label: event,
      icon: '⚡',
      requiresConfirmation: false,
      variant: 'primary' as const
    };
  };

  const config = getActionConfig(event);
  const finalVariant = variant || config.variant;

  const getButtonClasses = () => {
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';
    
    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base'
    };

    const variantClasses = {
      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300',
      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 disabled:bg-gray-300',
      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 disabled:bg-red-300',
      success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 disabled:bg-green-300'
    };

    return `${baseClasses} ${sizeClasses[size]} ${variantClasses[finalVariant]} ${className}`;
  };

  const handleClick = () => {
    if (config.requiresConfirmation) {
      setShowConfirmation(true);
    } else {
      handleAction();
    }
  };

  const handleAction = async () => {
    try {
      const actionMetadata: any = { ...metadata };

      if (config.requiresReason && reason.trim()) {
        actionMetadata.reason = reason.trim();
      }

      if (config.requiresChangesDescription && reason.trim()) {
        actionMetadata.changesDescription = reason.trim();
      }

      await onAction(event, voucherId, actionMetadata);
      setShowConfirmation(false);
      setReason('');
      setMetadata({});
    } catch (error) {
      console.error('Workflow action failed:', error);
      // Error handling will be done by parent component
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
    setReason('');
    setMetadata({});
  };

  const isReasonValid = () => {
    if (config.requiresReason || config.requiresChangesDescription) {
      return reason.trim().length >= 10;
    }
    return true;
  };

  return (
    <>
      <button
        onClick={handleClick}
        disabled={disabled || loading}
        className={getButtonClasses()}
        title={`${config.label} voucher`}
      >
        {loading ? (
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        ) : (
          <span className="mr-2">{config.icon}</span>
        )}
        {config.label}
      </button>

      {/* Confirmation Dialog */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                  <span className="text-2xl">{config.icon}</span>
                </div>
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Confirm {config.label}
                  </h3>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      Are you sure you want to {config.label.toLowerCase()} this voucher?
                    </p>
                  </div>
                </div>
              </div>

              {/* Reason Input */}
              {(config.requiresReason || config.requiresChangesDescription) && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {config.requiresChangesDescription ? 'Describe changes made:' : 'Reason:'}
                    <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder={config.requiresChangesDescription 
                      ? "Describe what changes were made to address the previous issues..."
                      : "Enter reason for this action..."
                    }
                    minLength={10}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Minimum 10 characters ({reason.length}/10)
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAction}
                  disabled={!isReasonValid()}
                  className={`px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                    isReasonValid()
                      ? `${getButtonClasses()} ${variantClasses[finalVariant]}`
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  Confirm {config.label}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

// Workflow Actions Group Component
interface WorkflowActionsGroupProps {
  actions: Array<{
    event: WorkflowEvent;
    name: string;
    requiresConfirmation: boolean;
    icon: string;
  }>;
  voucherId: string;
  onAction: (event: WorkflowEvent, voucherId: string, metadata?: any) => Promise<void>;
  loading?: boolean;
  className?: string;
}

export const WorkflowActionsGroup: React.FC<WorkflowActionsGroupProps> = ({
  actions,
  voucherId,
  onAction,
  loading = false,
  className = ''
}) => {
  if (actions.length === 0) {
    return null;
  }

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {actions.map((action) => (
        <WorkflowActionButton
          key={action.event}
          event={action.event}
          voucherId={voucherId}
          onAction={onAction}
          loading={loading}
          size="sm"
        />
      ))}
    </div>
  );
};

export default WorkflowActionButton;

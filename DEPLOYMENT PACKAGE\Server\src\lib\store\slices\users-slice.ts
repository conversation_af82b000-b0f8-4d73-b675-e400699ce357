
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { usersApi } from '@/lib/api';

export interface UsersSlice {
  users: AppState['users'];
  addUser: AppState['addUser'];
  updateUser: AppState['updateUser'];
  changePassword: AppState['changePassword'];
  resetUserPassword: AppState['resetUserPassword'];
  deleteUser: AppState['deleteUser'];
  fetchAllUsers: () => Promise<boolean>;
}

export const createUsersSlice: StateCreator<AppState, [], [], UsersSlice> = (set, get) => ({
  users: [],
  addUser: (user) => {
    // Update the state
    set((state) => ({
      users: [...state.users, user]
    }));

    // Force update to localStorage
    try {
      // Get the current localStorage data
      const storageData = localStorage.getItem('voucher-management-system');
      if (storageData) {
        const parsedData = JSON.parse(storageData);

        // Update the users in the parsed data
        if (parsedData.state) {
          if (!Array.isArray(parsedData.state.users)) {
            parsedData.state.users = [];
          }

          parsedData.state.users.push(user);

          // Save back to localStorage
          localStorage.setItem('voucher-management-system', JSON.stringify(parsedData));
          console.log(`User ${user.name} added to localStorage`);
        }
      }

      return true;
    } catch (error) {
      console.error('Error updating localStorage:', error);
      return false;
    }
  },
  updateUser: async (userId, userData) => {
    try {
      // First call the API to update the user in the database
      console.log(`Updating user ${userId} with data:`, userData);
      const updatedUser = await usersApi.updateUser(userId, userData);
      console.log('User updated in database:', updatedUser);

      // Then update the state with the response from the server
      set((state) => ({
        users: state.users.map(user =>
          user.id === userId ? { ...user, ...updatedUser } : user
        )
      }));

      // Then ensure the update is persisted to localStorage
      try {
        const storeData = JSON.parse(localStorage.getItem('voucher-management-system') || '{}');
        if (storeData && storeData.state && storeData.state.users) {
          const users = storeData.state.users;
          const userIndex = users.findIndex((u: any) => u.id === userId);
          if (userIndex !== -1) {
            users[userIndex] = { ...users[userIndex], ...updatedUser };
            storeData.state.users = users;
            localStorage.setItem('voucher-management-system', JSON.stringify(storeData));
          }
        }
        return true;
      } catch (error) {
        console.error('Error updating user in localStorage:', error);
        return false;
      }
    } catch (error) {
      console.error('Error updating user in database:', error);
      return false;
    }
  },
  changePassword: (department, newPassword) => {
    // In a real app, this would call an API to change the password
    console.log(`Password for ${department} changed to ${newPassword}`);
  },

  resetUserPassword: (userId, newPassword) => {
    // Get the current state
    const currentState = get();
    const userToUpdate = currentState.users.find(user => user.id === userId);

    if (!userToUpdate) {
      console.error(`User with ID ${userId} not found`);
      return false;
    }

    // Update the user in the state with the new password
    // Make sure the password is set as a string (not undefined or null)
    const updatedPassword = newPassword || ''; // Ensure we never set undefined/null

    console.log(`Setting new password for user ${userToUpdate.name}: ${updatedPassword}`);

    set(state => ({
      users: state.users.map(user =>
        user.id === userId ? { ...user, password: updatedPassword } : user
      )
    }));

    // Force update to localStorage
    try {
      // Get the current localStorage data
      const storageData = localStorage.getItem('voucher-management-system');
      if (storageData) {
        const parsedData = JSON.parse(storageData);

        // Update the user in the parsed data
        if (parsedData.state && Array.isArray(parsedData.state.users)) {
          const userIndex = parsedData.state.users.findIndex((u: any) => u.id === userId);
          if (userIndex !== -1) {
            parsedData.state.users[userIndex] = {
              ...parsedData.state.users[userIndex],
              password: updatedPassword
            };

            // Save back to localStorage
            localStorage.setItem('voucher-management-system', JSON.stringify(parsedData));
            console.log(`Password reset for user ${userToUpdate.name} saved to localStorage`);
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Error updating localStorage:', error);
      return false;
    }
  },

  deleteUser: (userId) => {
    // Update the state
    set(state => ({
      users: state.users.filter(user => user.id !== userId)
    }));

    // Force update to localStorage
    try {
      // Get the current localStorage data
      const storageData = localStorage.getItem('voucher-management-system');
      if (storageData) {
        const parsedData = JSON.parse(storageData);

        // Update the users in the parsed data
        if (parsedData.state && Array.isArray(parsedData.state.users)) {
          parsedData.state.users = parsedData.state.users.filter((u: any) => u.id !== userId);

          // Save back to localStorage
          localStorage.setItem('voucher-management-system', JSON.stringify(parsedData));
          console.log(`User with ID ${userId} deleted from localStorage`);
        }
      }

      return true;
    } catch (error) {
      console.error('Error updating localStorage:', error);
      return false;
    }
  },

  // Fetch all users from the backend API
  fetchAllUsers: async () => {
    try {
      console.log('🔄 fetchAllUsers: Starting API call...');

      // Call the API to get all users
      const users = await usersApi.getAllUsers();

      console.log('🔄 fetchAllUsers: API returned', users.length, 'users');
      console.log('🔄 fetchAllUsers: Users data:', users.map(u => `${u.name} (${u.department}) - Active: ${u.isActive}`));

      // Update the state with the fetched users
      set({ users });

      // Verify the store was updated
      const storeUsers = get().users;
      console.log('🔄 fetchAllUsers: Store now has', storeUsers.length, 'users');

      // Check AUDIT users specifically
      const auditUsers = storeUsers.filter(u => u.department === 'AUDIT' && u.isActive);
      console.log('🔄 fetchAllUsers: AUDIT users in store:', auditUsers.map(u => u.name));

      console.log('✅ fetchAllUsers: Successfully updated store with users from backend');
      return true;
    } catch (error) {
      console.error('❌ fetchAllUsers: Error fetching users:', error);
      return false;
    }
  },
});

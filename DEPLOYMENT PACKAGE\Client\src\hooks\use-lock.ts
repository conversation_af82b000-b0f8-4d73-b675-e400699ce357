import { useState, useCallback } from 'react';
import { Department } from '@/lib/types';

// Simple in-memory lock system
const activeLocks: Record<string, string> = {};

// Maximum concurrent users per department
const MAX_CONCURRENT_USERS: Record<Department, number> = {
  'FINANCE': 4,
  'AUDIT': 6,
  'MINISTRIES': 4,
  'PENSIONS': 4,
  'PENTMEDIA': 4,
  'MISSIONS': 4,
  'PENTSOS': 4,
  'ADMINISTRATOR': 1,
  'SYSTEM ADMIN': 1
};

export function useLock() {
  const [currentLock, setCurrentLock] = useState<string | null>(null);

  // Try to acquire a lock for a specific section
  const tryAcquireLock = useCallback(async (department: Department, sectionId: string): Promise<boolean> => {
    try {
      const lockKey = `${department}:${sectionId}`;
      
      // Count existing locks for this department
      const departmentLocks = Object.keys(activeLocks).filter(key => key.startsWith(`${department}:`));
      
      // Check if we've reached the maximum concurrent users for this department
      if (departmentLocks.length >= (MAX_CONCURRENT_USERS[department] || 4)) {
        // If this user already has a lock for this section, allow them to keep it
        if (activeLocks[lockKey] === sessionStorage.getItem('userId')) {
          return true;
        }
        return false;
      }
      
      // If the lock is available or already owned by this user
      if (!activeLocks[lockKey] || activeLocks[lockKey] === sessionStorage.getItem('userId')) {
        // Generate a unique user ID if not already present
        if (!sessionStorage.getItem('userId')) {
          sessionStorage.setItem('userId', `user-${Date.now()}`);
        }
        
        // Acquire the lock
        activeLocks[lockKey] = sessionStorage.getItem('userId')!;
        setCurrentLock(lockKey);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error acquiring lock:', error);
      return false;
    }
  }, []);

  // Release a lock
  const tryReleaseLock = useCallback(async (department: Department, sectionId: string): Promise<boolean> => {
    try {
      const lockKey = `${department}:${sectionId}`;
      
      // Only allow the lock owner to release it
      if (activeLocks[lockKey] === sessionStorage.getItem('userId')) {
        delete activeLocks[lockKey];
        if (currentLock === lockKey) {
          setCurrentLock(null);
        }
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error releasing lock:', error);
      return false;
    }
  }, [currentLock]);

  return { tryAcquireLock, tryReleaseLock, currentLock };
}

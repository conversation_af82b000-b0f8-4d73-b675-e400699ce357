@echo off
echo ============================================================
echo VMS PRODUCTION DATABASE CLEANUP FOR DEPLOYMENT
echo ============================================================
echo.
echo This script will:
echo - Remove ALL vouchers and related data
echo - Remove ALL users except admin accounts
echo - Reset system to deployment-ready state
echo - Keep only ADMIN user (password: enter123)
echo.
echo WARNING: This action cannot be undone!
echo.
set /p confirm="Are you sure you want to proceed? (type YES to continue): "
if /i not "%confirm%"=="YES" (
    echo.
    echo Cleanup cancelled by user.
    pause
    exit /b 1
)

echo.
echo ============================================================
echo STARTING DATABASE CLEANUP...
echo ============================================================

echo.
echo Connecting to MySQL and executing cleanup script...
mysql -u root -p vms_production < cleanup-for-deployment.sql

if %errorlevel% equ 0 (
    echo.
    echo ============================================================
    echo DATABASE CLEANUP COMPLETED SUCCESSFULLY!
    echo ============================================================
    echo.
    echo Status: READY FOR PRODUCTION DEPLOYMENT
    echo.
    echo Admin Login Details:
    echo Username: ADMIN
    echo Password: enter123
    echo Department: SYSTEM ADMIN
    echo.
    echo The database is now clean and ready for deployment.
    echo All vouchers, batches, and user data have been removed.
    echo Only the admin account remains.
    echo.
) else (
    echo.
    echo ============================================================
    echo ERROR: DATABASE CLEANUP FAILED!
    echo ============================================================
    echo.
    echo Please check:
    echo 1. MySQL is running
    echo 2. Database credentials are correct
    echo 3. vms_production database exists
    echo.
)

echo.
pause

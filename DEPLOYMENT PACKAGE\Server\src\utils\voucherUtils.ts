
import { Voucher } from '@/lib/types';

export const filterVouchers = (
  vouchers: Voucher[],
  searchTerm: string,
  sortColumn: string | null,
  sortDirection: 'asc' | 'desc'
) => {
  return vouchers.filter(voucher => {
    if (!searchTerm) return true;
    
    return (
      voucher.claimant.toLowerCase().includes(searchTerm.toLowerCase()) ||
      voucher.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      voucher.voucherId.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }).sort((a, b) => {
    if (!sortColumn) return 0;
    
    let valueA = a[sortColumn as keyof Voucher];
    let valueB = b[sortColumn as keyof Voucher];
    
    if (sortColumn === 'amount' || sortColumn === 'preAuditedAmount') {
      valueA = Number(valueA) || 0;
      valueB = Number(valueB) || 0;
    } else if (typeof valueA === 'string' && typeof valueB === 'string') {
      valueA = valueA.toLowerCase();
      valueB = valueB.toLowerCase();
    }
    
    if (valueA < valueB) return sortDirection === 'asc' ? -1 : 1;
    if (valueA > valueB) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });
};

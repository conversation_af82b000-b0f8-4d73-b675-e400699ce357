
import { Check, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { Voucher } from '@/lib/types';

interface VoucherCardProps {
  voucher: Voucher;
  isSelected: boolean;
  isRejected: boolean;
  showRejectionDialog: boolean;
  rejectionComment: string;
  onProcess: () => void;
  onReject: (comment?: string) => void;
  onOpenRejectionDialog: () => void;
  onCloseRejectionDialog: () => void;
  onRejectionCommentChange: (comment: string) => void;
}

export function VoucherCard({
  voucher,
  isSelected,
  isRejected,
  showRejectionDialog,
  rejectionComment,
  onProcess,
  onReject,
  onOpenRejectionDialog,
  onCloseRejectionDialog,
  onRejectionCommentChange
}: VoucherCardProps) {
  // Determine if this is a returning voucher
  const isReturningVoucher = voucher.isReturned || voucher.pendingReturn;

  // Determine if this is a resubmitted voucher
  const isResubmittedVoucher = voucher.isResubmitted ||
                              voucher.status === 'RE-SUBMISSION' ||
                              (voucher.rejectedBy && voucher.rejectedBy.trim() !== '');

  return (
    <div className={`p-4 border rounded-lg transition-colors ${
      isSelected ? 'bg-green-50 border-green-500 dark:bg-green-900/20 dark:border-green-500' :
      isRejected ? 'bg-red-50 border-red-500 dark:bg-red-900/20 dark:border-red-500' :
      'bg-card border-muted'
    }`}>
      <div className="flex flex-col space-y-4">
        <div className="flex justify-between">
          <div>
            <div className="text-lg font-semibold uppercase">{voucher.voucherId}</div>
            <div className="text-sm text-muted-foreground uppercase">{voucher.date}</div>
          </div>
          <div className="flex space-x-2 items-start">
            {isResubmittedVoucher && (
              <Badge className="uppercase bg-purple-500 text-white">
                RESUBMISSION
              </Badge>
            )}
            {isReturningVoucher && (
              <Badge variant="secondary" className="uppercase bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300">
                RETURNING
              </Badge>
            )}
            <Badge variant="outline" className="uppercase">
              {typeof voucher.amount === 'number' ? voucher.amount.toFixed(2) : parseFloat(voucher.amount || '0').toFixed(2)} {voucher.currency}
            </Badge>
          </div>
        </div>

        <div>
          <div className="text-sm font-semibold uppercase">CLAIMANT:</div>
          <div className="text-sm uppercase">{voucher.claimant}</div>
        </div>

        <div>
          <div className="text-sm font-semibold uppercase">DESCRIPTION:</div>
          <div className="text-sm uppercase">{voucher.description}</div>
        </div>

        {isReturningVoucher && voucher.returnComment && (
          <div>
            <div className="text-sm font-semibold uppercase text-amber-800 dark:text-amber-300">RETURN REASON:</div>
            <div className="text-sm uppercase p-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded">
              {voucher.returnComment}
            </div>
          </div>
        )}

        <div className="flex space-x-2 justify-end mt-4">
          {!isSelected && !isRejected ? (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={onOpenRejectionDialog}
                className="uppercase"
              >
                <X className="h-4 w-4 mr-1" />
                REJECT
              </Button>
              <Button
                size="sm"
                onClick={onProcess}
                className="uppercase bg-green-600 hover:bg-green-700"
              >
                <Check className="h-4 w-4 mr-1" />
                ACCEPT
              </Button>
            </>
          ) : isSelected ? (
            <Button
              size="sm"
              variant="outline"
              onClick={onProcess}
              className="uppercase border-green-500 text-green-500"
            >
              UNDO ACCEPT
            </Button>
          ) : (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onReject()}
              className="uppercase border-red-500 text-red-500"
            >
              UNDO REJECT
            </Button>
          )}
        </div>
      </div>

      <Dialog open={showRejectionDialog} onOpenChange={(open) => !open && onCloseRejectionDialog()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="uppercase">REJECT VOUCHER {voucher.voucherId}</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-2">
              <div className="text-sm font-semibold uppercase">REASON FOR REJECTION (REQUIRED):</div>
              <Textarea
                value={rejectionComment}
                onChange={(e) => onRejectionCommentChange(e.target.value.toUpperCase())}
                className="h-32 resize-none uppercase"
                placeholder="ENTER REASON FOR REJECTION"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={onCloseRejectionDialog} className="uppercase">
              CANCEL
            </Button>
            <Button
              onClick={() => {
                if (rejectionComment.trim()) {
                  onReject(rejectionComment);
                  onCloseRejectionDialog();
                }
              }}
              disabled={!rejectionComment.trim()}
              className="uppercase"
              variant="destructive"
            >
              CONFIRM REJECTION
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

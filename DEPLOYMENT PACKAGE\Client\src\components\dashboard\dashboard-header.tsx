
import { useNavigate } from 'react-router-dom';
import { NotificationsMenu } from '@/components/notifications';
import { UserNav } from '@/components/user-nav';
import { ModeToggle } from '@/components/mode-toggle';
import { ExitButton } from '@/components/exit-button';
import { OfflineStatus } from '@/components/offline-status';
import { useAppStore } from '@/lib/store';
import { ActiveUsersDisplay } from './active-users-display';

export function DashboardHeader() {
  const currentUser = useAppStore((state) => state.currentUser);

  if (!currentUser) return null;

  return (
    <header className="border-b border-gray-800 shrink-0">
      <div className="flex h-16 items-center px-6">
        <h1 className="text-lg font-semibold text-white flex items-center">
          {currentUser.department} DEPARTMENT DASHBOARD
        </h1>

        {/* Active users display */}
        <div className="ml-4">
          <ActiveUsersDisplay department={currentUser.department} />
        </div>

        <div className="ml-auto flex items-center space-x-4">
          <OfflineStatus />
          <NotificationsMenu />
          <ModeToggle />
          <UserNav />
          <ExitButton />
        </div>
      </div>
    </header>
  );
}

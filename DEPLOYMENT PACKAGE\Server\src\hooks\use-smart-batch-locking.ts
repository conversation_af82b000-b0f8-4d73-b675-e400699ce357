import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { getSocket } from '@/lib/socket';
import { useAppStore } from '@/lib/store';

export interface BatchOperationLock {
  operationType: 'batch-dispatch' | 'batch-receive' | 'bulk-operation';
  department: string;
  voucherIds?: string[];
  lockKey: string;
  isActive: boolean;
}

/**
 * Smart Background Locking Hook for Finance Department
 * 
 * Provides automatic, invisible locking for batch operations without changing
 * existing workflow or UI. Only shows notifications when conflicts occur.
 */
export function useSmartBatchLocking(department: string) {
  const [activeLocks, setActiveLocks] = useState<Map<string, BatchOperationLock>>(new Map());
  const [isOperationInProgress, setIsOperationInProgress] = useState(false);
  const { toast } = useToast();
  const currentUser = useAppStore(state => state.currentUser);

  /**
   * Generate a unique lock key for the operation
   */
  const generateLockKey = useCallback((operationType: string, department: string, suffix?: string): string => {
    const timestamp = Date.now();
    const baseLockKey = `${operationType}:${department}`;
    return suffix ? `${baseLockKey}:${suffix}:${timestamp}` : `${baseLockKey}:${timestamp}`;
  }, []);

  /**
   * Attempt to acquire a batch operation lock
   */
  const acquireBatchLock = useCallback(async (
    operationType: 'batch-dispatch' | 'batch-receive' | 'bulk-operation',
    voucherIds?: string[]
  ): Promise<{ success: boolean; lockKey?: string }> => {
    console.log(`🔄 SMART LOCK: Acquiring ${operationType} lock for ${department}`);

    const socket = getSocket();
    if (!socket || !currentUser) {
      console.log(`❌ SMART LOCK: No socket or user available for lock acquisition`);
      return { success: false };
    }

    const lockKey = generateLockKey(operationType, department, voucherIds?.join('-'));
    console.log(`🔄 SMART LOCK: Generated lock key: ${lockKey}`);

    return new Promise((resolve) => {
      console.log(`🔄 SMART LOCK: Emitting lock_request to server...`);

      // Add timeout for lock acquisition
      const timeout = setTimeout(() => {
        console.log(`❌ SMART LOCK: Lock acquisition timeout after 10 seconds`);
        resolve({ success: false });
      }, 10000);

      // CRITICAL ARCHITECTURE FIX: Always use the user's own department for locking
      // AUDIT users working in Department Voucher Hubs should use AUDIT locks
      // FINANCE users working in their department should use FINANCE locks
      const userDepartment = currentUser?.department || department;

      console.log(`🔒 SMART LOCK: ${userDepartment} user requesting lock for their own department`);

      socket.emit('department_lock_request', {
        targetDepartment: userDepartment
      }, (response: { success: boolean; message?: string }) => {
        clearTimeout(timeout);
        console.log(`🔄 SMART LOCK: Received lock response:`, response);

        if (response.success) {
          // Lock acquired successfully
          const batchLock: BatchOperationLock = {
            operationType,
            department,
            voucherIds,
            lockKey,
            isActive: true
          };

          setActiveLocks(prev => new Map(prev.set(lockKey, batchLock)));
          
          console.log(`🔒 SMART LOCK: Acquired ${operationType} lock for ${department}:`, lockKey);
          resolve({ success: true, lockKey });
        } else {
          // Lock acquisition failed - another user is performing similar operation
          console.log(`❌ SMART LOCK: Failed to acquire ${operationType} lock:`, response.message);

          // Show notification to user for lock conflicts
          toast({
            title: "Please Wait",
            description: response.message || "Another user is performing a similar operation.",
            variant: "default",
          });

          resolve({ success: false });
        }
      });
    });
  }, [currentUser, department, generateLockKey, toast]);

  /**
   * Release a batch operation lock
   */
  const releaseBatchLock = useCallback(async (lockKey: string): Promise<void> => {
    const socket = getSocket();
    if (!socket) return;

    return new Promise((resolve) => {
      // CRITICAL ARCHITECTURE FIX: Always release the user's own department lock
      const userDepartment = currentUser?.department || department;

      console.log(`🔓 SMART LOCK: ${userDepartment} user releasing lock for their own department`);

      socket.emit('department_lock_release', {
        targetDepartment: userDepartment
      }, (response: { success: boolean }) => {
        if (response.success) {
          setActiveLocks(prev => {
            const newLocks = new Map(prev);
            newLocks.delete(lockKey);
            return newLocks;
          });

          console.log(`🔓 SMART LOCK: Released department lock for:`, department);
        }
        resolve();
      });
    });
  }, [department, currentUser]);

  /**
   * Execute a batch operation with automatic locking
   */
  const executeWithBatchLock = useCallback(async <T>(
    operationType: 'batch-dispatch' | 'batch-receive' | 'bulk-operation',
    operation: () => Promise<T>,
    voucherIds?: string[]
  ): Promise<T | null> => {
    console.log(`🔄 SMART LOCK: Starting ${operationType} for department ${department}`);

    if (isOperationInProgress) {
      console.log(`❌ SMART LOCK: Operation already in progress for ${operationType}`);
      toast({
        title: "Operation in Progress",
        description: "Please wait for the current operation to complete.",
        variant: "default",
      });
      return null;
    }

    setIsOperationInProgress(true);

    try {
      // Attempt to acquire lock
      console.log(`🔄 SMART LOCK: Attempting to acquire ${operationType} lock...`);
      const lockResult = await acquireBatchLock(operationType, voucherIds);
      console.log(`🔄 SMART LOCK: Lock acquisition result:`, lockResult);

      if (!lockResult.success) {
        // Lock acquisition failed - user was notified by acquireBatchLock
        console.log(`❌ SMART LOCK: Failed to acquire lock for ${operationType}`);
        return null;
      }

      // Execute the operation
      console.log(`🚀 SMART LOCK: Executing ${operationType} with lock protection`);
      const result = await operation();
      console.log(`✅ SMART LOCK: Operation ${operationType} completed successfully`);

      // Release lock after successful operation
      if (lockResult.lockKey) {
        await releaseBatchLock(lockResult.lockKey);
      }

      return result;
    } catch (error) {
      console.error(`❌ SMART LOCK: Error during ${operationType}:`, error);
      
      // Release lock on error
      const activeLockEntries = Array.from(activeLocks.entries());
      const currentLock = activeLockEntries.find(([_, lock]) => lock.operationType === operationType);
      if (currentLock) {
        await releaseBatchLock(currentLock[0]);
      }
      
      throw error;
    } finally {
      setIsOperationInProgress(false);
    }
  }, [isOperationInProgress, acquireBatchLock, releaseBatchLock, activeLocks, toast]);

  /**
   * Check if a specific operation type is currently locked
   */
  const isOperationLocked = useCallback((operationType: 'batch-dispatch' | 'batch-receive' | 'bulk-operation'): boolean => {
    return Array.from(activeLocks.values()).some(lock => 
      lock.operationType === operationType && lock.isActive
    );
  }, [activeLocks]);

  /**
   * Force clear stale locks for the current user's department
   */
  const forceClearStaleLocks = useCallback(async (): Promise<boolean> => {
    const socket = getSocket();
    if (!socket || !currentUser) {
      return false;
    }

    const userDepartment = currentUser.department;
    console.log(`🔧 FORCE CLEAR: Attempting to clear stale locks for ${userDepartment}`);

    return new Promise((resolve) => {
      socket.emit('department_lock_release', {
        targetDepartment: userDepartment
      }, (response: { success: boolean }) => {
        if (response.success) {
          console.log(`✅ FORCE CLEAR: Successfully cleared stale locks for ${userDepartment}`);
          // Clear local locks as well
          setActiveLocks(new Map());
          resolve(true);
        } else {
          console.log(`❌ FORCE CLEAR: Failed to clear stale locks for ${userDepartment}`);
          resolve(false);
        }
      });
    });
  }, [currentUser]);

  return {
    executeWithBatchLock,
    isOperationLocked,
    isOperationInProgress,
    activeLocks: Array.from(activeLocks.values()),
    forceClearStaleLocks
  };
}

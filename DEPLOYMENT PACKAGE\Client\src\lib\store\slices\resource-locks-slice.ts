import { StateCreator } from 'zustand';
import { AppState, ResourceLock } from '../types';

export interface ResourceLocksState {
  resourceLocks: ResourceLock[];
  updateResourceLock: (lockKey: string, isLocked: boolean, userId?: string) => void;
  updateResourceLocks: (locks: ResourceLock[]) => void;
}

export type ResourceLocksSlice = ResourceLocksState;

export const createResourceLocksSlice: StateCreator<
  AppState,
  [],
  [],
  ResourceLocksSlice
> = (set, get) => ({
  resourceLocks: [],
  
  updateResourceLock: (lockKey, isLocked, userId) => {
    const currentUser = get().currentUser;
    const users = get().users;
    
    if (isLocked && userId) {
      // Find user info
      const user = users.find(u => u.id === userId);
      const userName = user?.name || 'Unknown User';
      const department = user?.department || 'Unknown Department';
      
      // Update or add lock
      set((state) => {
        // Check if lock already exists
        const existingLockIndex = state.resourceLocks.findIndex(lock => lock.key === lockKey);
        
        if (existingLockIndex >= 0) {
          // Update existing lock
          const updatedLocks = [...state.resourceLocks];
          updatedLocks[existingLockIndex] = {
            ...updatedLocks[existingLockIndex],
            userId,
            userName,
            department,
            expiresAt: Date.now() + 5 * 60 * 1000, // 5 minutes
          };
          return { resourceLocks: updatedLocks };
        } else {
          // Add new lock
          const [resourceType, resourceId] = lockKey.split(':');
          const newLock: ResourceLock = {
            key: lockKey,
            userId,
            userName,
            department,
            expiresAt: Date.now() + 5 * 60 * 1000, // 5 minutes
            resourceType,
            resourceId,
            remainingTime: 5 * 60 // 5 minutes in seconds
          };
          return { resourceLocks: [...state.resourceLocks, newLock] };
        }
      });
    } else {
      // Remove lock
      set((state) => ({
        resourceLocks: state.resourceLocks.filter(lock => lock.key !== lockKey)
      }));
    }
  },
  
  updateResourceLocks: (locks) => {
    set({ resourceLocks: locks });
  }
});

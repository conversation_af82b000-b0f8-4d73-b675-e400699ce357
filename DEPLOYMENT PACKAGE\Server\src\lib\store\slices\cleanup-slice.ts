
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { formatCurrentDate } from '../utils';

export interface CleanupSlice {
  cleanupExpiredRejectedVouchers: () => void;
  cleanupExpiredReturnedVouchers: () => void;
}

export const createCleanupSlice: StateCreator<AppState, [], [], CleanupSlice> = (set, get) => ({
  // This function will clean up rejected vouchers older than 1 month (30 days)
  cleanupExpiredRejectedVouchers: () => {
    const currentTime = new Date();
    const oneMonthAgo = new Date(currentTime.getTime() - 30 * 24 * 60 * 60 * 1000);

    console.log("Running cleanup for rejected vouchers");
    console.log("Current time:", currentTime);
    console.log("Cutoff time (1 month ago):", oneMonthAgo);
    
    set((state) => {
      // Filter vouchers that are rejected and older than 1 month (30 days)
      const vouchersToDelete = state.vouchers.filter(voucher => {
        // IMPORTANT: Only consider vouchers with VOUCHER REJECTED status
        if (voucher.status !== "VOUCHER REJECTED" || !voucher.rejectionTime) {
          return false;
        }

        // Skip vouchers that don't have a proper rejection time
        if (!voucher.rejectionTime) {
          console.log(`Voucher ${voucher.voucherId} has no rejection time, skipping`);
          return false;
        }

        try {
          // Try to parse the rejection time
          // Format can be something like "JUN 15, 2023 AT 2:30PM"
          const dateString = voucher.rejectionTime.split(' AT ')[0];
          const [monthStr, dayStr, yearStr] = dateString.split(/[ ,]+/);

          const months = {
            "JAN": 0, "FEB": 1, "MAR": 2, "APR": 3, "MAY": 4, "JUN": 5,
            "JUL": 6, "AUG": 7, "SEP": 8, "OCT": 9, "NOV": 10, "DEC": 11
          };

          const month = months[monthStr as keyof typeof months];
          const day = parseInt(dayStr);
          const year = parseInt(yearStr);

          if (isNaN(day) || isNaN(year) || month === undefined) {
            console.error(`Invalid date format: ${voucher.rejectionTime}`);
            return false;
          }

          const rejectionDate = new Date(year, month, day);
          const isOldEnough = rejectionDate < oneMonthAgo;

          if (isOldEnough) {
            console.log(`Voucher ${voucher.voucherId} rejected on ${rejectionDate.toISOString()} is older than 1 month and will be deleted`);
          }

          return isOldEnough;
        } catch (error) {
          console.error(`Error parsing rejection time: ${voucher.rejectionTime}`, error);
          return false;
        }
      });

      // If there are vouchers to delete, log and remove them
      if (vouchersToDelete.length > 0) {
        console.log(`Auto-deleting ${vouchersToDelete.length} rejected vouchers older than 1 month`);
        
        // Get the IDs of vouchers to delete
        const idsToDelete = vouchersToDelete.map(v => v.id);
        
        // Return updated state with rejected vouchers removed
        return {
          vouchers: state.vouchers.filter(voucher => !idsToDelete.includes(voucher.id))
        };
      }
      
      console.log("No rejected vouchers to delete at this time");
      return state; // No vouchers to delete
    });
  },
  
  // This function will clean up returned vouchers older than 14 days
  cleanupExpiredReturnedVouchers: () => {
    const currentTime = new Date();
    const fourteenDaysAgo = new Date(currentTime.getTime() - 14 * 24 * 60 * 60 * 1000);
    
    set((state) => {
      // Filter vouchers that are returned and older than 14 days
      const vouchersToDelete = state.vouchers.filter(voucher => {
        if (!voucher.isReturned || !voucher.returnTime) {
          return false;
        }
        
        try {
          // Try to parse the return time
          const dateString = voucher.returnTime.split(' AT ')[0];
          const [monthStr, dayStr, yearStr] = dateString.split(/[ ,]+/);
          
          const months = {
            "JAN": 0, "FEB": 1, "MAR": 2, "APR": 3, "MAY": 4, "JUN": 5,
            "JUL": 6, "AUG": 7, "SEP": 8, "OCT": 9, "NOV": 10, "DEC": 11
          };
          
          const month = months[monthStr as keyof typeof months];
          const day = parseInt(dayStr);
          const year = parseInt(yearStr);
          
          if (isNaN(day) || isNaN(year) || month === undefined) {
            console.error(`Invalid date format: ${voucher.returnTime}`);
            return false;
          }
          
          const returnDate = new Date(year, month, day);
          return returnDate < fourteenDaysAgo;
        } catch (error) {
          console.error(`Error parsing return time: ${voucher.returnTime}`, error);
          return false;
        }
      });
      
      // If there are vouchers to delete, log and remove them
      if (vouchersToDelete.length > 0) {
        console.log(`Auto-deleting ${vouchersToDelete.length} returned vouchers older than 14 days`);
        
        // Get the IDs of vouchers to delete
        const idsToDelete = vouchersToDelete.map(v => v.id);
        
        // Return updated state with returned vouchers removed
        return {
          vouchers: state.vouchers.filter(voucher => !idsToDelete.includes(voucher.id))
        };
      }
      
      return state; // No vouchers to delete
    });
  }
});

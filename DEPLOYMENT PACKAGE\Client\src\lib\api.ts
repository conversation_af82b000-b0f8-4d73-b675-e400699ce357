import axios from 'axios';
import { withRetry, isRetryableError } from './api-retry';

// Dynamic API instance with automatic server discovery
const api = axios.create({
  baseURL: '/api', // Start with proxy, will be updated dynamically
  timeout: 15000, // Increased timeout for LAN discovery
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // FIXED: Include session cookies in all requests
});

// PRODUCTION ARCHITECTURE: Use same-origin API calls
const configureApiBaseUrl = async () => {
  try {
    // Test current server API
    const apiTest = await fetch('/api/health', {
      method: 'GET',
      signal: AbortSignal.timeout(3000)
    });

    if (apiTest.ok) {
      console.log('🔧 Using single-server production mode');
      return;
    }
  } catch (error) {
    console.warn('⚠️ API health check failed:', error);
  }

  // PRODUCTION: API is served from same origin, no discovery needed
  console.log('✅ Using same-origin API at /api');
};

// Initialize API configuration
configureApiBaseUrl().catch(error => {
  console.error('❌ Failed to configure API:', error);
});

// FIXED: Session-based authentication using cookies
api.interceptors.request.use(
  (config) => {
    // REMOVED: Session ID header - using cookies instead
    // Session cookies are automatically included with withCredentials: true
    config.headers['X-Request-Time'] = Date.now().toString();
    return config;
  },
  (error) => Promise.reject(error)
);

// SIMPLE: Response interceptor with retry logic for LAN deployment
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // SIMPLE: Add retry logic for retryable errors
    if (isRetryableError(error) && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        console.warn('🔄 Retrying failed API request:', originalRequest.url);
        return await withRetry(() => api.request(originalRequest), {
          maxRetries: 2,
          delayMs: 1000
        });
      } catch (retryError) {
        console.error('❌ API retry failed:', retryError);
        // Fall through to original error handling
      }
    }

    // Handle network connectivity issues
    if (!error.response) {
      console.error('Network error:', error.message);
      return Promise.reject({
        type: 'network',
        message: 'Cannot connect to server. Please check if the server computer is on.',
        originalError: error
      });
    }

    // PRODUCTION FIX: Handle authentication errors (401) with complete cleanup
    if (error.response.status === 401) {
      console.log('🚨 401 UNAUTHORIZED: Performing complete session cleanup');

      // Clear all cookies by setting them to expire
      document.cookie.split(";").forEach(function(c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
      console.log('🧹 CLEANUP: All cookies cleared');

      // Clear all localStorage
      try {
        localStorage.clear();
        console.log('🧹 CLEANUP: localStorage cleared');
      } catch (e) {
        console.warn('Failed to clear localStorage:', e);
      }

      // Clear sessionStorage
      try {
        sessionStorage.clear();
        console.log('🧹 CLEANUP: sessionStorage cleared');
      } catch (e) {
        console.warn('Failed to clear sessionStorage:', e);
      }

      // WEBSOCKET FIX: Handle session invalidation from browser close detection
      const message = error.response.data.message || 'Your session has expired. Please log in again.';

      // Store ONLY the auth error message (after clearing everything else)
      localStorage.setItem('auth_error', message);

      // Redirect to login page immediately
      console.warn('🔐 Session invalid - redirecting to clean login');
      window.location.href = '/';

      return Promise.reject({
        type: 'auth',
        message: message,
        status: 401,
        originalError: error
      });
    }

    // Handle server errors (500)
    if (error.response.status === 500) {
      console.error('Server error:', error.response.data);
      return Promise.reject({
        type: 'server',
        message: 'System error. Please try again or contact IT support.',
        originalError: error
      });
    }

    return Promise.reject(error);
  }
);

// Simplified Auth API for LAN deployment
export const authApi = {
  login: async (credentials: { department: string, username: string, password: string }) => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },
  register: async (name: string, password: string, department: string) => {
    const response = await api.post('/auth/register', { name, password, department });
    return response.data;
  },
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },
  logout: async () => {
    // Simple logout for LAN (no tokens to clear)
    try {
      const response = await api.post('/auth/logout');
      return response.data;
    } catch (error) {
      // Non-critical for LAN deployment
      console.warn('Logout API call failed (non-critical):', error);
      return { success: true };
    }
  },
};

// Users API
export const usersApi = {
  getAllUsers: async () => {
    // Add timestamp to prevent caching
    const timestamp = new Date().getTime();
    const response = await api.get(`/users?_t=${timestamp}`);
    return response.data;
  },
  getUserById: async (id: string) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },
  createUser: async (userData: any) => {
    const response = await api.post('/users', userData);
    return response.data;
  },
  updateUser: async (id: string, userData: any) => {
    // Ensure isActive is properly formatted for the server
    if (userData.isActive !== undefined) {
      // Convert to boolean and ensure it's sent as a boolean value
      userData = {
        ...userData,
        isActive: Boolean(userData.isActive)
      };
      console.log(`Sending isActive as ${userData.isActive} (${typeof userData.isActive})`);
    }

    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },
  changePassword: async (id: string, currentPassword: string, newPassword: string) => {
    const response = await api.put(`/users/${id}/password`, { currentPassword, newPassword });
    return response.data;
  },
  resetPassword: async (id: string, newPassword: string) => {
    const response = await api.put(`/users/${id}/reset-password`, { newPassword });
    return response.data;
  },
  // Get users for password change requests (public endpoint)
  getPublicUsers: async () => {
    const response = await api.get('/users/public/users');
    return response.data;
  },
  // Request password change (for admin approval)
  requestPasswordChange: async (requestData: {
    userId: string;
    name: string;
    department: string;
    newPassword: string;
  }) => {
    const response = await api.post('/users/request-password-change', requestData);
    return response.data;
  },
  deleteUser: async (id: string) => {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  },
  // Get all password change requests (admin only)
  getPasswordChangeRequests: async () => {
    const response = await api.get('/password-change-requests');
    return response.data;
  },
  // Approve password change request (admin only)
  approvePasswordChangeRequest: async (requestId: string) => {
    const response = await api.put(`/password-change-requests/${requestId}/approve`, {});
    return response.data;
  },
  // Reject password change request (admin only)
  rejectPasswordChangeRequest: async (requestId: string, reason?: string) => {
    const response = await api.put(`/password-change-requests/${requestId}/reject`, { reason });
    return response.data;
  },

  // Audit Attachments API
  // Upload attachments to voucher
  uploadVoucherAttachments: async (voucherId: string, files: FileList) => {
    const formData = new FormData();
    Array.from(files).forEach(file => {
      formData.append('files', file);
    });

    const response = await api.post(`/audit/vouchers/${voucherId}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get attachments for voucher
  getVoucherAttachments: async (voucherId: string) => {
    const response = await api.get(`/audit/vouchers/${voucherId}/attachments`);
    return response.data;
  },

  // Download attachment
  downloadAttachment: async (attachmentId: string) => {
    const response = await api.get(`/audit/attachments/${attachmentId}/download`, {
      responseType: 'blob',
    });
    return response;
  },

  // View attachment (returns blob for viewing)
  viewAttachment: async (attachmentId: string) => {
    const response = await api.get(`/audit/attachments/${attachmentId}/view`, {
      responseType: 'blob',
    });
    return response;
  },

  // Delete attachment
  deleteAttachment: async (attachmentId: string) => {
    const response = await api.delete(`/audit/attachments/${attachmentId}`);
    return response.data;
  },

  // Get attachment count for voucher (lightweight check)
  getVoucherAttachmentCount: async (voucherId: string) => {
    try {
      const attachments = await usersApi.getVoucherAttachments(voucherId);
      return attachments.length;
    } catch (error) {
      return 0; // Return 0 if error (e.g., no access or no attachments)
    }
  },
  getPendingRegistrations: async () => {
    // Add timestamp to prevent caching
    const timestamp = new Date().getTime();
    const response = await api.get(`/users/registrations/pending?_t=${timestamp}`, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    console.log(`Fetched ${response.data.length} pending registrations from API`);
    return response.data;
  },
  approveRegistration: async (id: string) => {
    const response = await api.post(`/users/registrations/${id}/approve`);
    return response.data;
  },
  rejectRegistration: async (id: string) => {
    const response = await api.post(`/users/registrations/${id}/reject`);
    return response.data;
  },
};

// Vouchers API
export const vouchersApi = {
  getAllVouchers: async (department?: string, timestamp?: number) => {
    // If department is 'ALL', don't include department parameter to get all vouchers
    const params: any = (department && department !== 'ALL') ? { department } : {};
    console.log(`Fetching vouchers with params:`, params);

    // Add a timestamp to prevent caching (use provided timestamp or generate new one)
    params.timestamp = timestamp || new Date().getTime();

    try {
      console.log(`Fetching vouchers from API for department: ${department || 'all'} at ${new Date().toISOString()}`);

      const response = await api.get('/vouchers', {
        params,
        // Add cache control headers
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      console.log(`Received ${response.data.length} vouchers from API for department: ${department || 'all'}`);

      // Check if we have any vouchers (only log for non-AUDIT departments to reduce noise)
      if (response.data.length === 0 && department !== 'AUDIT') {
        console.warn(`No vouchers returned for department: ${department || 'all'}`);
      }

      // Log the first few vouchers for debugging
      if (response.data.length > 0) {
        console.log('First few vouchers:', response.data.slice(0, 3).map((v: any) => ({
          id: v.id,
          voucherId: v.voucher_id,
          department: v.department,
          status: v.status
        })));

        // Check for missing voucher IDs (gaps in sequence)
        const voucherIds = response.data
          .map((v: any) => v.voucher_id)
          .filter(Boolean)
          .sort();

        if (voucherIds.length > 1) {
          // Group by month prefix
          const vouchersByPrefix: Record<string, string[]> = {};
          voucherIds.forEach((id: string) => {
            const prefix = id.substring(0, 3);
            if (!vouchersByPrefix[prefix]) vouchersByPrefix[prefix] = [];
            vouchersByPrefix[prefix].push(id);
          });

          // Check for gaps in each prefix group
          Object.entries(vouchersByPrefix).forEach(([prefix, ids]) => {
            if (ids.length > 1) {
              // Extract numeric parts and check for gaps
              const numericParts = ids.map(id => parseInt(id.substring(3)));
              numericParts.sort((a, b) => a - b);

              // Check for gaps
              for (let i = 1; i < numericParts.length; i++) {
                if (numericParts[i] - numericParts[i-1] > 1) {
                  console.warn(`Gap detected in voucher sequence: ${prefix}${numericParts[i-1].toString().padStart(5, '0')} -> ${prefix}${numericParts[i].toString().padStart(5, '0')}`);
                }
              }

              // Check if the first voucher is missing
              if (numericParts[0] > 1) {
                console.warn(`First voucher may be missing! First voucher in sequence is ${prefix}${numericParts[0].toString().padStart(5, '0')}`);
              }
            }
          });
        }
      }

      // Map server response to client model to ensure consistent property names
      const mappedVouchers = response.data.map((v: any) => ({
        id: v.id,
        voucherId: v.voucher_id,
        date: v.date,
        claimant: v.claimant,
        description: v.description,
        amount: v.amount,
        currency: v.currency,
        department: v.department,
        dispatchedBy: v.dispatched_by,
        dispatchTime: v.dispatch_time,
        status: v.status,
        sentToAudit: v.sent_to_audit,
        createdBy: v.created_by,
        comment: v.comment,
        returnComment: v.return_comment,
        isReturned: v.is_returned,
        pendingReturn: v.pending_return,
        deleted: v.deleted,
        // Include all other fields
        ...v
      }));

      // Log success
      console.log(`Successfully mapped ${mappedVouchers.length} vouchers for department: ${department || 'all'}`);

      return mappedVouchers;
    } catch (error) {
      console.error(`Error fetching vouchers for department ${department || 'all'}:`, error);
      throw error;
    }
  },
  getVoucherById: async (id: string) => {
    const response = await api.get(`/vouchers/${id}`);
    return response.data;
  },
  createVoucher: async (voucherData: any) => {
    try {
      console.log('🔄 Creating voucher with data:', voucherData);

      // PRODUCTION FIX: Ensure data is properly serialized
      const cleanData = {
        claimant: voucherData.claimant,
        description: voucherData.description,
        amount: voucherData.amount,
        currency: voucherData.currency,
        department: voucherData.department,
        idempotencyKey: voucherData.idempotencyKey
      };

      console.log('🔄 Sending clean data:', cleanData);
      console.log('🔄 Department being sent:', cleanData.department);
      console.log('🔄 Department type:', typeof cleanData.department);

      const response = await api.post('/vouchers', cleanData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Voucher created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating voucher:', error);
      if (error.response) {
        console.error('❌ Response data:', error.response.data);
        console.error('❌ Response status:', error.response.status);
      }
      throw error;
    }
  },
  updateVoucher: async (id: string, voucherData: any) => {
    const response = await api.put(`/vouchers/${id}`, voucherData);
    return response.data;
  },
  deleteVoucher: async (id: string) => {
    const response = await api.delete(`/vouchers/${id}`);
    return response.data;
  },
  sendToAudit: async (id: string) => {
    const response = await api.post(`/vouchers/${id}/send-to-audit`);
    return response.data;
  },

  certifyVoucher: async (id: string) => {
    const response = await api.post(`/vouchers/${id}/certify`);
    return response.data;
  },
  rejectVoucher: async (id: string, comment: string) => {
    const response = await api.post(`/vouchers/${id}/reject`, { comment });
    return response.data;
  },
  getBlacklistedVoucherIds: async () => {
    const response = await api.get('/vouchers/blacklist/ids');
    return response.data;
  },
  addBlacklistedVoucherId: async (voucherId: string) => {
    const response = await api.post('/vouchers/blacklist/ids', { voucherId });
    return response.data;
  },

  // HOLD FEATURE: Toggle voucher hold status (pure overlay)
  toggleVoucherHold: async (voucherId: string, holdData: { isOnHold: boolean; holdComment: string }) => {
    const response = await api.put(`/vouchers/${voucherId}/toggle-hold`, holdData);
    return response.data;
  },
};

// Batches API
export const batchesApi = {
  getAllBatches: async (department?: string) => {
    const params = department ? { department } : {};
    const response = await api.get('/batches', { params });
    return response.data;
  },
  getBatchById: async (id: string) => {
    const response = await api.get(`/batches/${id}`);
    return response.data;
  },
  createBatch: async (batchData: any) => {
    const response = await api.post('/batches', batchData);
    return response.data;
  },
  receiveBatch: async (id: string, receivedVoucherIds: string[], rejectedVoucherIds: string[], rejectionComments: Record<string, string> = {}) => {
    const response = await api.post(`/batches/${id}/receive`, { receivedVoucherIds, rejectedVoucherIds, rejectionComments });
    return response.data;
  },
};

// Provisional Cash API
export const provisionalCashApi = {
  getAllRecords: async (department?: string) => {
    const params = department ? { department } : {};
    const response = await api.get('/provisional-cash', { params });
    return response.data;
  },
  getRecordById: async (id: string) => {
    const response = await api.get(`/provisional-cash/${id}`);
    return response.data;
  },
  createRecord: async (recordData: any) => {
    const response = await api.post('/provisional-cash', recordData);
    return response.data;
  },
  updateRecord: async (id: string, recordData: any) => {
    const response = await api.put(`/provisional-cash/${id}`, recordData);
    return response.data;
  },
  deleteRecord: async (id: string) => {
    const response = await api.delete(`/provisional-cash/${id}`);
    return response.data;
  },
};

// Notifications API
export const notificationsApi = {
  getAllNotifications: async () => {
    const response = await api.get('/notifications');
    return response.data;
  },
  getUnreadCount: async () => {
    const response = await api.get('/notifications/unread/count');
    return response.data;
  },
  markAsRead: async (id: string) => {
    const response = await api.put(`/notifications/${id}/read`);
    return response.data;
  },
  markAllAsRead: async () => {
    const response = await api.put('/notifications/read/all');
    return response.data;
  },
  createNotification: async (notificationData: any) => {
    const response = await api.post('/notifications', notificationData);
    return response.data;
  },
  deleteNotification: async (id: string) => {
    const response = await api.delete(`/notifications/${id}`);
    return response.data;
  },
};

// Admin API
export const adminApi = {
  getSystemSettings: async () => {
    const response = await api.get('/admin/settings');
    return response.data;
  },
  updateSystemSettings: async (settingsData: any) => {
    const response = await api.put('/admin/settings', settingsData);
    return response.data;
  },
  backupDatabase: async () => {
    const response = await api.post('/admin/backup');
    return response.data;
  },
  restoreDatabase: async (filename: string) => {
    const response = await api.post('/admin/restore', { filename });
    return response.data;
  },
  getBackups: async () => {
    const response = await api.get('/admin/backups');
    return response.data;
  },
  getAuditLogs: async (params: any = {}) => {
    const response = await api.get('/admin/audit-logs', { params });
    return response.data;
  },
};

// Audit API
export const auditApi = {
  getDashboardData: async () => {
    const response = await api.get('/audit/dashboard');
    return response.data;
  },
  getAnalyticsData: async (params: any = {}) => {
    const response = await api.get('/audit/analytics', { params });
    return response.data;
  },
  logAuditAction: async (actionData: any) => {
    const response = await api.post('/audit/log', actionData);
    return response.data;
  },
  getResourceLogs: async (resourceType: string, resourceId: string) => {
    const response = await api.get(`/audit/logs/${resourceType}/${resourceId}`);
    return response.data;
  },
};

export default {
  auth: authApi,
  users: usersApi,
  vouchers: vouchersApi,
  batches: batchesApi,
  provisionalCash: provisionalCashApi,
  notifications: notificationsApi,
  admin: adminApi,
  audit: auditApi,
};

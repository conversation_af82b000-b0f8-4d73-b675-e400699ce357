
import { Button } from '@/components/ui/button';
import { formatNumberWithCommas } from '@/utils/formatUtils';
import { UnifiedVoucherBadges } from '@/components/common/UnifiedVoucherBadges';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import React, { useRef, useEffect, useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Voucher } from '@/lib/types';
import { Trash2, RefreshCw, Eye } from 'lucide-react';
import { SortableColumnHeader } from './sortable-column-header';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogHeader } from '@/components/ui/dialog';
import { useAppStore } from '@/lib/store';
import { toast } from 'sonner';

interface ReturnedVouchersTabProps {
  filteredVouchers: Voucher[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  handleDeleteReturnedVoucher: (voucherId: string) => void;
  onViewVoucher?: (voucher: Voucher) => void;
  onAddBackToSubmission?: (voucher: Voucher) => void;
  isAudit?: boolean;
}

export function ReturnedVouchersTab({
  filteredVouchers,
  sortColumn,
  sortDirection,
  handleSort,
  handleDeleteReturnedVoucher,
  onViewVoucher,
  onAddBackToSubmission,
  isAudit = false
}: ReturnedVouchersTabProps) {
  const [selectedVoucher, setSelectedVoucher] = useState<Voucher | null>(null);
  // Refs for synchronized scrolling
  const headerRef = useRef<HTMLDivElement>(null);
  const bodyRef = useRef<HTMLDivElement>(null);

  // Set up synchronized scrolling between header and body
  useEffect(() => {
    const headerElement = headerRef.current;
    const bodyElement = bodyRef.current;

    if (!headerElement || !bodyElement) return;

    const handleHeaderScroll = () => {
      if (bodyElement) {
        bodyElement.scrollLeft = headerElement.scrollLeft;
      }
    };

    const handleBodyScroll = () => {
      if (headerElement) {
        headerElement.scrollLeft = bodyElement.scrollLeft;
      }
    };

    headerElement.addEventListener('scroll', handleHeaderScroll);
    bodyElement.addEventListener('scroll', handleBodyScroll);

    return () => {
      headerElement.removeEventListener('scroll', handleHeaderScroll);
      bodyElement.removeEventListener('scroll', handleBodyScroll);
    };
  }, []);

  const handleViewVoucher = (voucher: Voucher) => {
    if (onViewVoucher) {
      onViewVoucher(voucher);
    } else {
      setSelectedVoucher(voucher);
    }
  };

  const handleCloseDialog = () => {
    setSelectedVoucher(null);
  };

  // Function to add back to pending
  const updateVoucher = useAppStore(state => state.updateVoucher);

  const handleAddBack = (voucher: Voucher) => {
    // We only allow departments to add returned vouchers back to pending
    if (isAudit) return;

    try {
      console.log(`Adding returned voucher ${voucher.id} (${voucher.voucherId}) back to pending submission`);

      // APPROACH 1: Preserve the original voucher ID when adding returned vouchers back to PENDING
      // This maintains traceability and history of the voucher throughout its lifecycle

      // RESUBMISSION WORKFLOW FIX: Follow normal workflow path with resubmission flag
      const currentDate = new Date().toLocaleDateString();
      updateVoucher(voucher.id, {
        status: "PENDING SUBMISSION", // Normal pending status
        workflow_state: "FINANCE_PENDING", // Normal workflow state
        sentToAudit: false,
        // CRITICAL FIX: Clear return visibility flags to remove from RETURNED tab
        isReturned: false,
        returnTime: undefined,
        returnComment: undefined,
        return_certified_visible_to_finance: 0, // Remove from Finance Department RETURNED tab
        deleted: false,
        // CRITICAL FIX: Ensure resubmission flag is properly sent to server
        isResubmitted: true, // Client-side flag
        is_resubmitted: 1, // Server-side flag - CRITICAL FOR FIXES TO WORK
        lastResubmissionDate: new Date().toISOString(),
        // Reset dispatch fields but preserve return/rejection history for traceability
        auditDispatchTime: undefined,
        auditDispatchedBy: undefined,
        dispatched: false,
        dispatchTime: undefined,
        dispatchedBy: undefined,
        batch_id: null, // Clear batch association
        batchId: null, // Also clear camelCase version
        // RETURN VOUCHER RESUBMISSION: Preserve original return reason in dedicated fields
        original_return_reason: voucher.returnComment || voucher.comment || voucher.original_return_reason,
        originalReturnReason: voucher.returnComment || voucher.comment || voucher.originalReturnReason,
        original_returned_by: voucher.returnedBy || voucher.returned_by || voucher.originalReturnedBy,
        originalReturnedBy: voucher.returnedBy || voucher.returned_by || voucher.originalReturnedBy,
        // Update comment to indicate resubmission from return
        comment: `Re-added from return on ${currentDate}`,
        // PRESERVE RETURN HISTORY: Keep for audit trail and badge logic
        // returned_by: preserved (if exists)
        // return_time: preserved (if exists)
        // is_returned_voucher: preserved (for badge logic)
      });

      toast.success(`Voucher ${voucher.voucherId} re-added from returned to pending submissions`, {
        duration: 3000
      });

      if (onAddBackToSubmission) {
        onAddBackToSubmission(voucher);
      }
    } catch (error) {
      toast.error('Failed to add voucher back to pending', {
        duration: 3000
      });
    }
  };

  // Function to format currency values
  const formatCurrency = (amount: number, currency: string) => {
    return `${formatNumberWithCommas(amount)} ${currency}`;
  };

  // Helper function to display the comment correctly
  const getDisplayComment = (voucher: Voucher): string => {
    // Debug log to inspect comment values
    console.log(`Voucher ${voucher.id} comment values:`, {
      returnComment: voucher.returnComment,
      comment: voucher.comment,
      returnCommentType: typeof voucher.returnComment,
      commentType: typeof voucher.comment
    });

    // First try to use returnComment (the specific field for returns)
    if (voucher.returnComment) {
      return String(voucher.returnComment);
    }

    // Then try the general comment field
    if (voucher.comment) {
      return String(voucher.comment);
    }

    // Default fallback
    return "NO COMMENT PROVIDED";
  };

  return (
    <div className="space-y-2">
      <div className="flex flex-col rounded-md border">
        {/* Fixed header */}
        <div className="sticky top-0 z-10 bg-background overflow-hidden">
          <div ref={headerRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
            <thead>
              <tr className="bg-background">
                <SortableColumnHeader
                  title="VOUCHER ID"
                  sortKey="voucherId"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <SortableColumnHeader
                  title="DATE"
                  sortKey="date"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <SortableColumnHeader
                  title="CLAIMANT"
                  sortKey="claimant"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <SortableColumnHeader
                  title="DESCRIPTION"
                  sortKey="description"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <SortableColumnHeader
                  title="AMOUNT"
                  sortKey="amount"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap"
                />
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10">RETURN TIME</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10">COMMENT</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10">ACTIONS</TableHead>
              </tr>
            </thead>
            </table>
          </div>
        </div>

        {/* PRODUCTION FIX: Enhanced scrollable body with adaptive height */}
        <div
          className="overflow-auto scrollbar-visible"
          style={{
            height: `${Math.max(400, Math.min(window.innerHeight * 0.7, filteredVouchers.length * 56 + 100))}px`,
            minHeight: '400px',
            maxHeight: '80vh',
            scrollbarWidth: 'thin',
            overflowY: 'auto',
            overflowX: 'auto'
          }}
        >
          <div ref={bodyRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px', paddingBottom: '3rem' }}>
              <tbody>
              {filteredVouchers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center uppercase">
                    NO RETURNED VOUCHERS FOUND.
                  </TableCell>
                </TableRow>
              ) : (
                filteredVouchers.map((voucher) => (
                  <TableRow
                    key={voucher.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleViewVoucher(voucher)}
                  >
                    <TableCell className="font-medium uppercase whitespace-nowrap">
                      <div className="flex flex-col items-center gap-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="block truncate">{voucher.voucherId}</span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{voucher.voucherId}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        {/* PRODUCTION FIX: Missing badges in returned vouchers tab */}
                        <UnifiedVoucherBadges
                          voucher={voucher}
                          tabName="returned"
                          size="sm"
                          className="flex-wrap justify-center"
                        />
                      </div>
                    </TableCell>
                    <TableCell className="uppercase whitespace-nowrap">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.date}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.date}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="uppercase">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.claimant}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.claimant}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="max-w-[200px] truncate uppercase">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.description}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="uppercase whitespace-nowrap">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{formatCurrency(voucher.amount, voucher.currency)}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{formatCurrency(voucher.amount, voucher.currency)}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="uppercase whitespace-nowrap">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.returnTime || "N/A"}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.returnTime || "N/A"}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="uppercase max-w-[250px] truncate">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{getDisplayComment(voucher)}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{getDisplayComment(voucher)}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {onViewVoucher && (
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              onViewVoucher(voucher);
                            }}
                            title="View Voucher Details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}

                        {/* Only show Add Back button if not Audit department */}
                        {!isAudit && onAddBackToSubmission && (
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddBack(voucher);
                            }}
                            title="Add Back to Submission"
                            className="text-blue-500 hover:text-blue-700 hover:bg-blue-100"
                          >
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                        )}

                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteReturnedVoucher(voucher.id);
                          }}
                          title="Delete Voucher"
                          className="text-red-500 hover:text-red-700 hover:bg-red-100"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Voucher Details Dialog */}
      {selectedVoucher && (
        <Dialog open={!!selectedVoucher} onOpenChange={handleCloseDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="text-center">VOUCHER DETAILS</DialogTitle>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold">Voucher ID:</p>
                  <p>{selectedVoucher.voucherId}</p>
                </div>
                <div>
                  <p className="font-semibold">Date:</p>
                  <p>{selectedVoucher.dateReceived}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold">Claimant:</p>
                  <p>{selectedVoucher.claimant}</p>
                </div>
                <div>
                  <p className="font-semibold">Status:</p>
                  <p>{selectedVoucher.status}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold">Description:</p>
                  <p>{selectedVoucher.description}</p>
                </div>
                <div>
                  <p className="font-semibold">Pre-Audited Amount:</p>
                  <p>{formatCurrency(selectedVoucher.preAuditedAmount || selectedVoucher.amount, selectedVoucher.currency)}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold">Amount:</p>
                  <p>{formatCurrency(selectedVoucher.amount, selectedVoucher.currency)}</p>
                </div>
                <div>
                  <p className="font-semibold">Department:</p>
                  <p>{selectedVoucher.department}</p>
                </div>
              </div>

              {selectedVoucher.returnComment && (
                <div>
                  <p className="font-semibold">Return Comment:</p>
                  <p>{selectedVoucher.returnComment}</p>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

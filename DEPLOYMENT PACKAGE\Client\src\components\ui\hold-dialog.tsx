import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Pause, Play, AlertCircle } from 'lucide-react';

interface Voucher {
  id: string;
  voucherId?: string;
  voucher_id?: string;
  description?: string;
  amount?: number;
  isOnHold?: boolean;
  is_on_hold?: boolean;
  holdComment?: string;
  hold_comment?: string;
  holdBy?: string;
  holdTime?: string;
}

interface HoldDialogProps {
  open: boolean;
  onClose: () => void;
  voucher: Voucher | null;
  onToggleHold: (voucherId: string, isOnHold: boolean, holdComment: string) => Promise<void>;
}

export function HoldDialog({ open, onClose, voucher, onToggleHold }: HoldD<PERSON>ogProps) {
  const [holdComment, setHoldComment] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setHoldComment('');
    }
  }, [open]);

  if (!voucher) return null;

  const isCurrentlyOnHold = voucher.isOnHold === true || voucher.is_on_hold === true;
  const currentHoldComment = voucher.holdComment || voucher.hold_comment || '';
  const voucherDisplayId = voucher.voucherId || voucher.voucher_id || voucher.id;

  const handleToggleHold = async () => {
    // If putting on hold, require a comment
    if (!isCurrentlyOnHold && !holdComment.trim()) {
      return;
    }

    setIsProcessing(true);
    try {
      await onToggleHold(voucher.id, !isCurrentlyOnHold, holdComment.trim());
      onClose();
    } catch (error) {
      // Error handling is done by the parent component
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    if (isProcessing) return;
    setHoldComment('');
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isCurrentlyOnHold ? (
              <>
                <Play className="h-5 w-5 text-green-600" />
                Resume Voucher
              </>
            ) : (
              <>
                <Pause className="h-5 w-5 text-red-600" />
                Put Voucher on Hold
              </>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {isCurrentlyOnHold ? (
            <div className="space-y-4">
              {/* Hold Status Section */}
              <div className="bg-red-50 border border-red-200 p-4 rounded-md">
                <div className="flex items-center gap-2 text-red-800 mb-3">
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">Voucher {voucherDisplayId} is currently on hold</span>
                </div>

                {/* Hold Metadata */}
                <div className="space-y-2 text-sm">
                  {voucher.holdBy && (
                    <div className="flex justify-between">
                      <span className="font-medium text-red-700">Put on hold by:</span>
                      <span className="text-red-600">{voucher.holdBy}</span>
                    </div>
                  )}
                  {voucher.holdTime && (
                    <div className="flex justify-between">
                      <span className="font-medium text-red-700">Date & Time:</span>
                      <span className="text-red-600">{new Date(voucher.holdTime).toLocaleString()}</span>
                    </div>
                  )}
                  {currentHoldComment && (
                    <div className="mt-3">
                      <span className="font-medium text-red-700">Reason:</span>
                      <p className="text-red-600 mt-1 bg-white p-2 rounded border">{currentHoldComment}</p>
                    </div>
                  )}
                </div>

                <p className="text-sm text-red-600 mt-3 font-medium">
                  Click "Resume Voucher" below to remove the hold and allow normal processing.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Voucher Info */}
              <div className="bg-blue-50 border border-blue-200 p-3 rounded-md">
                <p className="text-sm font-medium text-blue-800">Putting voucher {voucherDisplayId} on hold</p>
                <p className="text-xs text-blue-600 mt-1">This will prevent any editing or processing until resumed.</p>
              </div>

              {/* Hold Comment Input */}
              <div className="space-y-2">
                <Label htmlFor="holdComment">
                  Reason for putting on hold <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="holdComment"
                  placeholder="Enter reason for putting this voucher on hold..."
                  value={holdComment}
                  onChange={(e) => setHoldComment(e.target.value)}
                  className="min-h-[80px]"
                  disabled={isProcessing}
                />
                {!holdComment.trim() && (
                  <p className="text-sm text-red-600">Please provide a reason for putting this voucher on hold.</p>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isProcessing}
          >
            Cancel
          </Button>
          <Button
            variant={isCurrentlyOnHold ? "default" : "destructive"}
            onClick={handleToggleHold}
            disabled={isProcessing || (!isCurrentlyOnHold && !holdComment.trim())}
            className={isCurrentlyOnHold 
              ? "bg-green-600 hover:bg-green-700" 
              : "bg-red-600 hover:bg-red-700"
            }
          >
            {isProcessing ? (
              "Processing..."
            ) : isCurrentlyOnHold ? (
              <>
                <Play className="h-4 w-4 mr-2" />
                Resume Voucher
              </>
            ) : (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Put on Hold
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

import { Department, Voucher } from '@/lib/types';
import { useVoucherFilters } from './use-voucher-filters';
import { useVoucherSelection } from './use-voucher-selection';
import { useDepartmentVoucherTabs } from './use-department-voucher-tabs';
import { useVoucherEditing } from './use-voucher-editing';
import { useVoucherDispatch } from './use-voucher-dispatch';
import { useVoucherDeletion } from './use-voucher-deletion';
import { useVoucherViewing } from './use-voucher-viewing';

export function useVoucherHub(department: Department) {

  // Get all the individual hooks
  const {
    searchTerm,
    setSearchTerm,
    sortColumn,
    sortDirection,
    handleSort,
    getFilteredVouchers
  } = useVoucherFilters();

  const {
    selectedVouchers,
    setSelectedVouchers,
    handleSelectVoucher,
    handleSelectAllVouchers
  } = useVoucherSelection();

  // Use the dedicated Department Voucher Tabs hook (production-ready)
  const {
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers
  } = useDepartmentVoucherTabs(department);
  
  const {
    voucherEdits,
    setVoucherEdits,
    showReturnCommentMap,
    setShowReturnCommentMap,
    handleVoucherEdit,
    handleSaveVoucherEdits,
    handleMarkForReturn,
    handleReturnToNew
  } = useVoucherEditing();
  
  const {
    showSendDialog,
    setShowSendDialog,
    isSending,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,
    handleSendToDepartment,
    handleConfirmSend
  } = useVoucherDispatch(department);
  
  const {
    handleDeleteReturnedVoucher,
    handleDeleteRejectedVoucher
  } = useVoucherDeletion();
  
  const {
    viewingVoucher,
    setViewingVoucher,
    handleViewVoucher
  } = useVoucherViewing();
  
  // Get the currently active vouchers based on the selected tab
  const getActiveVouchers = (): Voucher[] => {
    switch (activeTab) {
      case 'new-vouchers':
        return newVouchers;
      case 'pending-dispatch':
        return pendingDispatchVouchers;
      case 'dispatched':
        return dispatchedVouchers;
      case 'returned-vouchers':
        return returnedVouchers;
      case 'rejected-vouchers':
        return rejectedVouchers;
      default:
        return [];
    }
  };
  
  // Filter the active vouchers based on search and sort
  const filteredVouchers = getFilteredVouchers(getActiveVouchers());
  
  // Combined handlers that use multiple hooks
  const handleSendToDepartmentWithSelection = () => {
    handleSendToDepartment(
      selectedVouchers, 
      pendingDispatchVouchers.map(v => v.id)
    );
  };
  
  const handleConfirmSendWithSelection = () => {
    const newSelectedVouchers = handleConfirmSend(
      selectedVouchers, 
      pendingDispatchVouchers.map(v => v.id)
    );
    
    if (newSelectedVouchers !== null) {
      setSelectedVouchers(newSelectedVouchers);
    }
  };
  
  // Renamed from handleSelectAllVouchers to selectAllPendingVouchers to avoid conflict
  const selectAllPendingVouchers = () => {
    if (activeTab === 'pending-dispatch' && pendingDispatchVouchers.length > 0) {
      handleSelectAllVouchers(pendingDispatchVouchers.map(v => v.id));
    }
  };
  
  const handleDeleteVoucher = (voucherId: string) => {
    if (activeTab === 'returned-vouchers') {
      handleDeleteReturnedVoucher(voucherId);
    } else if (activeTab === 'rejected-vouchers') {
      handleDeleteRejectedVoucher(voucherId);
    }
  };
  
  // Special handler for marking a voucher for return
  const handleVoucherMarkForReturn = (voucherId: string, comment: string) => {
    const newTab = handleMarkForReturn(voucherId, comment);
    if (newTab) {
      setActiveTab(newTab);
    }
  };
  
  return {
    // From useVoucherFilters
    searchTerm,
    setSearchTerm,
    sortColumn,
    sortDirection,
    handleSort,

    // From useVoucherSelection
    selectedVouchers,
    setSelectedVouchers,
    handleSelectVoucher,

    // From useDepartmentVoucherTabs
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers,

    // From useVoucherEditing
    voucherEdits,
    setVoucherEdits,
    showReturnCommentMap,
    setShowReturnCommentMap,
    handleVoucherEdit,
    handleSaveVoucherEdits,
    handleReturnToNew,

    // From useVoucherDispatch
    showSendDialog,
    setShowSendDialog,
    isSending,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,

    // From useVoucherViewing
    viewingVoucher,
    setViewingVoucher,
    handleViewVoucher,

    // Computed values
    filteredVouchers,

    // Combined handlers
    handleSendToDepartment: handleSendToDepartmentWithSelection,
    handleConfirmSend: handleConfirmSendWithSelection,
    handleSelectAllVouchers: selectAllPendingVouchers, // Using renamed function
    handleDeleteVoucher,
    handleMarkForReturn: handleVoucherMarkForReturn
  };
}

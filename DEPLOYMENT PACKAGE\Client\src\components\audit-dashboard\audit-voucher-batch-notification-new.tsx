import { ArrowDownCircle, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AuditVoucherBatchNotificationProps {
  pendingBatchesCount: number;
  onReceiveVouchers: () => void;
}

export function AuditVoucherBatchNotification({ 
  pendingBatchesCount, 
  onReceiveVouchers
}: AuditVoucherBatchNotificationProps) {
  // Early return if there are no batches to display
  if (!pendingBatchesCount || pendingBatchesCount <= 0) return null;
  
  // Notification message
  const notificationMessage = `YOU HAVE NEW VOUCHER${pendingBatchesCount !== 1 ? 'S' : ''} FROM FINANCE: RECEIVE TO PROCEED.`;

  return (
    <div 
      className="bg-amber-900/20 p-4 rounded-lg mb-6 border border-amber-700/50"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ArrowDownCircle className="h-5 w-5 text-amber-500" />
          <div className="flex items-center">
            <p className="text-sm text-amber-300">
              {notificationMessage}
            </p>
            <Lock className="ml-2 h-4 w-4 text-amber-500" />
          </div>
        </div>
        <Button 
          size="sm" 
          onClick={onReceiveVouchers} 
          className="uppercase bg-amber-700 hover:bg-amber-800"
        >
          Receive Vouchers
        </Button>
      </div>
    </div>
  );
}
